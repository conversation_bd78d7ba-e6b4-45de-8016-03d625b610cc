// 多服务器网络质量监控 - JavaScript应用

class NetworkMonitor {
    constructor() {
        this.performanceChart = null;
        this.detailChart = null;
        this.refreshInterval = null;
        this.currentTimeRange = '1h';
        this.currentChartType = 'line';
        
        this.init();
    }
    
    init() {
        this.initCharts();
        this.loadData();
        this.startAutoRefresh();
        
        // 绑定事件
        window.addEventListener('beforeunload', () => {
            this.stopAutoRefresh();
        });
    }
    
    // 初始化图表
    initCharts() {
        // 性能趋势图
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        this.performanceChart = new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'TCP平均速度 (Mbps)',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'UDP平均速度 (Mbps)',
                        data: [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '网络速度趋势'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '速度 (Mbps)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                }
            }
        });
        
        // 详细统计图
        const detailCtx = document.getElementById('detailChart').getContext('2d');
        this.detailChart = new Chart(detailCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'TCP速度 (Mbps)',
                        data: [],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)'
                    },
                    {
                        label: 'UDP速度 (Mbps)',
                        data: [],
                        backgroundColor: 'rgba(231, 76, 60, 0.8)'
                    },
                    {
                        label: '丢包率 (%)',
                        data: [],
                        backgroundColor: 'rgba(241, 196, 15, 0.8)',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各服务器详细性能'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '速度 (Mbps)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '丢包率 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }
    
    // 加载数据
    async loadData() {
        try {
            // 模拟从SQLite数据库加载数据
            // 在实际环境中，这里应该调用后端API
            const data = await this.fetchDataFromDatabase();
            
            this.updateStatistics(data.statistics);
            this.updateServerGrid(data.servers);
            this.updateCharts(data.trends, data.details);
            
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('数据加载失败，请检查数据库连接');
        }
    }
    
    // 模拟从数据库获取数据
    async fetchDataFromDatabase() {
        // 在实际环境中，这里应该是对SQLite数据库的查询
        // 可以通过CGI脚本或者Node.js后端来实现
        
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    statistics: {
                        onlineServers: 11,
                        avgTcpSpeed: 847.5,
                        avgUdpSpeed: 94.2,
                        avgPacketLoss: 0.08,
                        lastUpdate: new Date().toLocaleTimeString()
                    },
                    servers: this.generateMockServerData(),
                    trends: this.generateMockTrendData(),
                    details: this.generateMockDetailData()
                });
            }, 1000);
        });
    }
    
    // 生成模拟服务器数据
    generateMockServerData() {
        const servers = [];
        for (let i = 1; i <= 12; i++) {
            const isOnline = Math.random() > 0.1; // 90%在线率
            servers.push({
                name: `Server-${i.toString().padStart(2, '0')}`,
                ip: `192.168.1.${100 + i}`,
                status: isOnline ? 'online' : 'offline',
                tcpSpeed: isOnline ? (Math.random() * 200 + 700).toFixed(1) : 0,
                udpSpeed: isOnline ? (Math.random() * 20 + 85).toFixed(1) : 0,
                packetLoss: isOnline ? (Math.random() * 0.5).toFixed(2) : 100,
                latency: isOnline ? (Math.random() * 10 + 5).toFixed(1) : 999,
                lastTest: isOnline ? new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString() : 'N/A'
            });
        }
        return servers;
    }
    
    // 生成模拟趋势数据
    generateMockTrendData() {
        const labels = [];
        const tcpData = [];
        const udpData = [];
        
        const now = new Date();
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 3600000);
            labels.push(time.getHours() + ':00');
            tcpData.push((Math.random() * 100 + 750).toFixed(1));
            udpData.push((Math.random() * 15 + 85).toFixed(1));
        }
        
        return { labels, tcpData, udpData };
    }
    
    // 生成模拟详细数据
    generateMockDetailData() {
        const labels = [];
        const tcpData = [];
        const udpData = [];
        const lossData = [];
        
        for (let i = 1; i <= 12; i++) {
            labels.push(`Server-${i.toString().padStart(2, '0')}`);
            tcpData.push((Math.random() * 200 + 700).toFixed(1));
            udpData.push((Math.random() * 20 + 85).toFixed(1));
            lossData.push((Math.random() * 0.5).toFixed(2));
        }
        
        return { labels, tcpData, udpData, lossData };
    }
    
    // 更新统计信息
    updateStatistics(stats) {
        document.getElementById('onlineServers').textContent = stats.onlineServers;
        document.getElementById('onlineServers').className = `number ${stats.onlineServers >= 10 ? 'good' : stats.onlineServers >= 8 ? 'warning' : 'danger'}`;
        
        document.getElementById('avgTcpSpeed').textContent = stats.avgTcpSpeed;
        document.getElementById('avgTcpSpeed').className = `number ${stats.avgTcpSpeed >= 500 ? 'good' : stats.avgTcpSpeed >= 200 ? 'warning' : 'danger'}`;
        
        document.getElementById('avgUdpSpeed').textContent = stats.avgUdpSpeed;
        document.getElementById('avgUdpSpeed').className = `number ${stats.avgUdpSpeed >= 80 ? 'good' : stats.avgUdpSpeed >= 50 ? 'warning' : 'danger'}`;
        
        document.getElementById('avgPacketLoss').textContent = stats.avgPacketLoss;
        document.getElementById('avgPacketLoss').className = `number ${stats.avgPacketLoss <= 0.1 ? 'good' : stats.avgPacketLoss <= 1 ? 'warning' : 'danger'}`;
        
        document.getElementById('lastUpdate').textContent = stats.lastUpdate;
    }
    
    // 更新服务器网格
    updateServerGrid(servers) {
        const grid = document.getElementById('serverGrid');
        grid.innerHTML = '';
        
        servers.forEach(server => {
            const card = document.createElement('div');
            card.className = 'server-card';
            
            const statusClass = server.status === 'online' ? 'status-online' : 
                               server.status === 'warning' ? 'status-warning' : 'status-offline';
            
            card.innerHTML = `
                <h4>
                    ${server.name}
                    <span class="status-indicator ${statusClass}"></span>
                </h4>
                <div class="metric">
                    <span>IP地址:</span>
                    <span>${server.ip}</span>
                </div>
                <div class="metric">
                    <span>TCP速度:</span>
                    <span>${server.tcpSpeed} Mbps</span>
                </div>
                <div class="metric">
                    <span>UDP速度:</span>
                    <span>${server.udpSpeed} Mbps</span>
                </div>
                <div class="metric">
                    <span>丢包率:</span>
                    <span>${server.packetLoss}%</span>
                </div>
                <div class="metric">
                    <span>延迟:</span>
                    <span>${server.latency} ms</span>
                </div>
                <div class="metric">
                    <span>最后测试:</span>
                    <span>${server.lastTest}</span>
                </div>
            `;
            
            grid.appendChild(card);
        });
    }
    
    // 更新图表
    updateCharts(trends, details) {
        // 更新趋势图
        this.performanceChart.data.labels = trends.labels;
        this.performanceChart.data.datasets[0].data = trends.tcpData;
        this.performanceChart.data.datasets[1].data = trends.udpData;
        this.performanceChart.update();
        
        // 更新详细图
        this.detailChart.data.labels = details.labels;
        this.detailChart.data.datasets[0].data = details.tcpData;
        this.detailChart.data.datasets[1].data = details.udpData;
        this.detailChart.data.datasets[2].data = details.lossData;
        this.detailChart.update();
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadData();
        }, 30000); // 30秒刷新一次
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    // 显示错误信息
    showError(message) {
        const grid = document.getElementById('serverGrid');
        grid.innerHTML = `<div style="text-align: center; color: #e74c3c; padding: 40px;">${message}</div>`;
    }
    
    // 更新时间范围
    updateTimeRange() {
        this.currentTimeRange = document.getElementById('timeRange').value;
        this.loadData();
    }
    
    // 更新图表类型
    updateChartType() {
        this.currentChartType = document.getElementById('chartType').value;
        this.performanceChart.config.type = this.currentChartType;
        this.performanceChart.update();
    }
    
    // 刷新数据
    refreshData() {
        this.loadData();
    }
    
    // 导出数据
    exportData() {
        // 在实际环境中，这里应该调用后端API导出数据
        const data = {
            timestamp: new Date().toISOString(),
            servers: this.generateMockServerData(),
            trends: this.generateMockTrendData(),
            details: this.generateMockDetailData()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `network_monitor_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 全局函数（供HTML调用）
let monitor;

function refreshData() {
    monitor.refreshData();
}

function exportData() {
    monitor.exportData();
}

function updateTimeRange() {
    monitor.updateTimeRange();
}

function updateChartType() {
    monitor.updateChartType();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    monitor = new NetworkMonitor();
});
