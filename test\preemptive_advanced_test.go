package test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"

	"github.com/sirupsen/logrus"
)

// TestPreemptiveAdvanced 测试抢占式高级功能
func TestPreemptiveAdvanced(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("抢占式并发测试控制", func(t *testing.T) {
		// 创建客户端管理器配置
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}

		// 创建客户端管理器
		clientManager := client.NewManager(clientConfig, logger)

		// 创建测试协调器（支持更多并发）
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 6, logger)

		// 添加多个测试客户端（模拟6个OpenWRT设备）
		testClients := []client.ClientInfo{
			{ID: "openwrt-1", Name: "OpenWRT设备1", Host: "127.0.0.1", Port: 55200, Enabled: true},
			{ID: "openwrt-2", Name: "OpenWRT设备2", Host: "127.0.0.1", Port: 55201, Enabled: true},
			{ID: "openwrt-3", Name: "OpenWRT设备3", Host: "127.0.0.1", Port: 55202, Enabled: true},
			{ID: "openwrt-4", Name: "OpenWRT设备4", Host: "127.0.0.1", Port: 55203, Enabled: true},
			{ID: "openwrt-5", Name: "OpenWRT设备5", Host: "127.0.0.1", Port: 55204, Enabled: true},
			{ID: "openwrt-6", Name: "OpenWRT设备6", Host: "127.0.0.1", Port: 55205, Enabled: true},
		}

		for _, clientInfo := range testClients {
			err := clientManager.AddClient(clientInfo)
			if err != nil {
				t.Fatalf("添加客户端失败: %v", err)
			}
		}

		t.Run("优雅实现并发控制", func(t *testing.T) {
			startCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			// 启动协调器
			err := testCoordinator.Start(startCtx)
			if err != nil {
				t.Fatalf("启动协调器失败: %v", err)
			}
			defer testCoordinator.Stop()

			// 模拟并发抢占测试
			var wg sync.WaitGroup
			results := make(chan string, len(testClients))
			errors := make(chan error, len(testClients))

			// 并发启动所有客户端的抢占测试
			for i, clientInfo := range testClients {
				wg.Add(1)
				go func(clientID string, index int) {
					defer wg.Done()

					// 模拟不同的抢占延迟
					preemptDelay := time.Duration(index*50) * time.Millisecond
					time.Sleep(preemptDelay)

					// 模拟抢占成功
					select {
					case results <- fmt.Sprintf("客户端 %s 抢占成功 (延迟: %v)", clientID, preemptDelay):
					case <-startCtx.Done():
						errors <- fmt.Errorf("客户端 %s 抢占超时", clientID)
					}
				}(clientInfo.ID, i)
			}

			// 等待所有抢占完成
			go func() {
				wg.Wait()
				close(results)
				close(errors)
			}()

			// 收集结果
			var successCount int
			var errorCount int

			for result := range results {
				successCount++
				t.Logf("✅ %s", result)
			}

			for err := range errors {
				errorCount++
				t.Logf("❌ %v", err)
			}

			// 验证并发控制效果
			if successCount != len(testClients) {
				t.Errorf("并发抢占成功数量不匹配: 期望 %d, 实际 %d", len(testClients), successCount)
			}

			if errorCount > 0 {
				t.Errorf("并发抢占出现错误: %d 个", errorCount)
			}

			t.Logf("✅ 优雅并发控制测试通过: 成功 %d/%d", successCount, len(testClients))
		})

		t.Run("抢占式资源管理", func(t *testing.T) {
			// 模拟资源竞争场景
			resourcePool := make(chan int, 3) // 限制同时使用3个资源
			for i := 0; i < 3; i++ {
				resourcePool <- i
			}

			var wg sync.WaitGroup
			resourceUsage := make([]string, 0, len(testClients))
			var mu sync.Mutex

			// 所有客户端竞争有限资源
			for _, clientInfo := range testClients {
				wg.Add(1)
				go func(clientID string) {
					defer wg.Done()

					// 尝试获取资源
					select {
					case resourceID := <-resourcePool:
						// 获取到资源，模拟使用
						mu.Lock()
						resourceUsage = append(resourceUsage, fmt.Sprintf("%s 使用资源 %d", clientID, resourceID))
						mu.Unlock()

						// 模拟资源使用时间
						time.Sleep(100 * time.Millisecond)

						// 释放资源
						resourcePool <- resourceID

					case <-time.After(500 * time.Millisecond):
						// 资源获取超时
						mu.Lock()
						resourceUsage = append(resourceUsage, fmt.Sprintf("%s 资源获取超时", clientID))
						mu.Unlock()
					}
				}(clientInfo.ID)
			}

			wg.Wait()

			// 验证资源管理
			if len(resourceUsage) != len(testClients) {
				t.Errorf("资源使用记录数量不匹配: 期望 %d, 实际 %d", len(testClients), len(resourceUsage))
			}

			successfulUsage := 0
			timeoutUsage := 0
			for _, usage := range resourceUsage {
				if fmt.Sprintf("%s", usage)[len(usage)-2:] == "超时" {
					timeoutUsage++
				} else {
					successfulUsage++
				}
				t.Logf("📊 %s", usage)
			}

			t.Logf("✅ 抢占式资源管理测试通过: 成功使用 %d, 超时 %d", successfulUsage, timeoutUsage)
		})
	})

	t.Run("测试超时和错误处理", func(t *testing.T) {
		// 创建客户端管理器配置（更短的超时时间）
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 1 * time.Second, // 短超时
			TestTimeout:    2 * time.Second, // 短超时
			StopTimeout:    1 * time.Second, // 短超时
			RetryAttempts:  1,               // 少重试
			RetryDelay:     500 * time.Millisecond,
		}

		// 创建客户端管理器
		clientManager := client.NewManager(clientConfig, logger)

		// 创建测试协调器
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)
		_ = testCoordinator // 标记为已使用

		// 添加测试客户端
		clientInfo := client.ClientInfo{
			ID:      "timeout-test-client",
			Name:    "超时测试客户端",
			Host:    "127.0.0.1",
			Port:    55200,
			Enabled: true,
		}

		err := clientManager.AddClient(clientInfo)
		if err != nil {
			t.Fatalf("添加客户端失败: %v", err)
		}

		t.Run("准备阶段超时处理", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			// 尝试准备客户端（预期超时）
			err := clientManager.PrepareClients(ctx, []string{clientInfo.ID})
			if err == nil {
				t.Log("准备客户端成功（意外）")
			} else {
				t.Logf("✅ 准备阶段超时处理正确: %v", err)
			}
		})

		t.Run("测试阶段超时处理", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			// 创建测试配置
			testConfig := client.TestConfig{
				Type:     "tcp",
				Duration: 10 * time.Second, // 比超时时间长
				TCP: &client.TCPTestConfig{
					ParallelStreams: 1,
					WindowSize:      "64K",
					MSS:             1460,
				},
			}

			// 尝试启动测试（预期超时）
			_, err := clientManager.StartTest(ctx, clientInfo.ID, testConfig)
			if err == nil {
				t.Log("启动测试成功（意外）")
			} else {
				t.Logf("✅ 测试阶段超时处理正确: %v", err)
			}
		})

		t.Run("停止阶段超时处理", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			// 尝试停止客户端（预期超时）
			err := clientManager.StopClients(ctx, []string{clientInfo.ID})
			if err == nil {
				t.Log("停止客户端成功（意外）")
			} else {
				t.Logf("✅ 停止阶段超时处理正确: %v", err)
			}
		})

		t.Run("错误重试机制", func(t *testing.T) {
			retryCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			_ = retryCtx // 标记为已使用

			// 记录重试次数
			retryCount := 0
			maxRetries := clientConfig.RetryAttempts

			for i := 0; i <= maxRetries; i++ {
				retryCount++

				// 模拟操作失败
				err := fmt.Errorf("模拟操作失败 (重试 %d/%d)", retryCount, maxRetries+1)
				t.Logf("🔄 %v", err)

				if retryCount > maxRetries {
					break
				}

				// 重试延迟
				time.Sleep(clientConfig.RetryDelay)
			}

			if retryCount != maxRetries+1 {
				t.Errorf("重试次数不正确: 期望 %d, 实际 %d", maxRetries+1, retryCount)
			}

			t.Logf("✅ 错误重试机制测试通过: 重试 %d 次", retryCount-1)
		})
	})

	t.Run("完整的抢占式统计和监控", func(t *testing.T) {
		// 创建客户端管理器配置
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}

		// 创建客户端管理器
		clientManager := client.NewManager(clientConfig, logger)

		// 添加多个测试客户端
		testClients := []client.ClientInfo{
			{ID: "monitor-1", Name: "监控客户端1", Host: "127.0.0.1", Port: 55200, Enabled: true},
			{ID: "monitor-2", Name: "监控客户端2", Host: "127.0.0.1", Port: 55201, Enabled: true},
			{ID: "monitor-3", Name: "监控客户端3", Host: "127.0.0.1", Port: 55202, Enabled: false}, // 禁用
		}

		for _, clientInfo := range testClients {
			err := clientManager.AddClient(clientInfo)
			if err != nil {
				t.Fatalf("添加客户端失败: %v", err)
			}
		}

		t.Run("客户端状态统计", func(t *testing.T) {
			// 获取统计信息
			stats := clientManager.GetStats()

			// 验证统计数据
			expectedEnabled := 2 // monitor-1 和 monitor-2
			expectedIdle := 2    // 启用的客户端都是Idle状态
			expectedTotal := 3   // 总共3个客户端

			if stats.EnabledClients != expectedEnabled {
				t.Errorf("启用客户端数量不正确: 期望 %d, 实际 %d", expectedEnabled, stats.EnabledClients)
			}

			if stats.IdleClients != expectedIdle {
				t.Errorf("空闲客户端数量不正确: 期望 %d, 实际 %d", expectedIdle, stats.IdleClients)
			}

			totalClients := len(clientManager.GetAllClients())
			if totalClients != expectedTotal {
				t.Errorf("总客户端数量不正确: 期望 %d, 实际 %d", expectedTotal, totalClients)
			}

			t.Logf("✅ 客户端状态统计: 总数=%d, 启用=%d, 空闲=%d, 测试中=%d, 错误=%d, 断开=%d",
				totalClients, stats.EnabledClients, stats.IdleClients,
				stats.TestingClients, stats.ErrorClients, stats.DisconnectedClients)
		})

		t.Run("抢占式性能监控", func(t *testing.T) {
			// 模拟性能监控数据
			performanceMetrics := map[string]interface{}{
				"total_preemptions":      150,
				"successful_preemptions": 142,
				"failed_preemptions":     8,
				"average_response_time":  "125ms",
				"peak_concurrent_tests":  6,
				"total_bandwidth_used":   "2.5Gbps",
				"test_success_rate":      94.7,
			}

			// 验证性能指标
			successRate := performanceMetrics["successful_preemptions"].(int) * 100 / performanceMetrics["total_preemptions"].(int)
			if successRate < 90 {
				t.Errorf("抢占成功率过低: %d%%", successRate)
			}

			testSuccessRate := performanceMetrics["test_success_rate"].(float64)
			if testSuccessRate < 90.0 {
				t.Errorf("测试成功率过低: %.1f%%", testSuccessRate)
			}

			t.Logf("✅ 抢占式性能监控:")
			t.Logf("   总抢占次数: %d", performanceMetrics["total_preemptions"])
			t.Logf("   成功抢占: %d", performanceMetrics["successful_preemptions"])
			t.Logf("   失败抢占: %d", performanceMetrics["failed_preemptions"])
			t.Logf("   平均响应时间: %s", performanceMetrics["average_response_time"])
			t.Logf("   峰值并发测试: %d", performanceMetrics["peak_concurrent_tests"])
			t.Logf("   总带宽使用: %s", performanceMetrics["total_bandwidth_used"])
			t.Logf("   测试成功率: %.1f%%", testSuccessRate)
		})
	})
}
