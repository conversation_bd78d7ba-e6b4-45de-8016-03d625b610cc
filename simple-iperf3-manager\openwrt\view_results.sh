#!/bin/bash

# 查看iperf3测试结果脚本
# 提供多种查看方式：最近结果、统计信息、详细查询等

DB_FILE="./iperf3_results.db"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查数据库文件
check_database() {
    if [ ! -f "$DB_FILE" ]; then
        echo -e "${RED}错误: 数据库文件不存在 ($DB_FILE)${NC}"
        echo "请先运行测试脚本生成数据"
        exit 1
    fi
}

# 显示最近的测试结果
show_recent() {
    local limit=${1:-10}
    echo -e "${BLUE}📊 最近 $limit 次测试结果${NC}"
    echo "================================"
    
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    id as 'ID',
    datetime(timestamp, 'localtime') as '测试时间',
    test_type as '类型',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%.2f Mbps', tcp_bandwidth_mbps)
        WHEN test_type = 'UDP' THEN printf('%.2f Mbps', udp_bandwidth_mbps)
        ELSE 'N/A'
    END as '带宽',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%d 次重传', COALESCE(tcp_retransmits, 0))
        WHEN test_type = 'UDP' THEN printf('%.2f%% 丢包', COALESCE(udp_lost_percent, 0))
        ELSE 'N/A'
    END as '质量指标',
    status as '状态'
FROM test_results 
ORDER BY timestamp DESC 
LIMIT $limit;
EOF
}

# 显示统计信息
show_statistics() {
    echo -e "${GREEN}📈 测试统计信息${NC}"
    echo "================================"
    
    # 总体统计
    echo -e "${CYAN}总体统计:${NC}"
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    COUNT(*) as '总测试次数',
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as '成功次数',
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as '失败次数',
    printf('%.1f%%', 
        CAST(COUNT(CASE WHEN status = 'completed' THEN 1 END) AS FLOAT) * 100.0 / COUNT(*)
    ) as '成功率'
FROM test_results;
EOF

    echo ""
    
    # TCP统计
    echo -e "${CYAN}TCP测试统计:${NC}"
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    COUNT(*) as 'TCP测试次数',
    printf('%.2f Mbps', AVG(tcp_bandwidth_mbps)) as '平均带宽',
    printf('%.2f Mbps', MAX(tcp_bandwidth_mbps)) as '最高带宽',
    printf('%.2f Mbps', MIN(tcp_bandwidth_mbps)) as '最低带宽',
    printf('%.1f', AVG(COALESCE(tcp_retransmits, 0))) as '平均重传次数'
FROM test_results 
WHERE test_type = 'TCP' AND status = 'completed';
EOF

    echo ""
    
    # UDP统计
    echo -e "${CYAN}UDP测试统计:${NC}"
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    COUNT(*) as 'UDP测试次数',
    printf('%.2f Mbps', AVG(udp_bandwidth_mbps)) as '平均带宽',
    printf('%.2f Mbps', MAX(udp_bandwidth_mbps)) as '最高带宽',
    printf('%.2f Mbps', MIN(udp_bandwidth_mbps)) as '最低带宽',
    printf('%.2f%%', AVG(COALESCE(udp_lost_percent, 0))) as '平均丢包率',
    printf('%.2f ms', AVG(COALESCE(udp_jitter_ms, 0))) as '平均抖动'
FROM test_results 
WHERE test_type = 'UDP' AND status = 'completed';
EOF
}

# 显示今日测试结果
show_today() {
    echo -e "${YELLOW}📅 今日测试结果${NC}"
    echo "================================"
    
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    datetime(timestamp, 'localtime') as '测试时间',
    test_type as '类型',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%.2f Mbps', tcp_bandwidth_mbps)
        WHEN test_type = 'UDP' THEN printf('%.2f Mbps', udp_bandwidth_mbps)
        ELSE 'N/A'
    END as '带宽',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%d 次', COALESCE(tcp_retransmits, 0))
        WHEN test_type = 'UDP' THEN printf('%.2f%%', COALESCE(udp_lost_percent, 0))
        ELSE 'N/A'
    END as '重传/丢包',
    status as '状态'
FROM test_results 
WHERE date(timestamp) = date('now')
ORDER BY timestamp DESC;
EOF
}

# 显示详细信息
show_detail() {
    local test_id=$1
    
    if [ -z "$test_id" ]; then
        echo -e "${RED}错误: 请提供测试ID${NC}"
        echo "用法: $0 detail <test_id>"
        return 1
    fi
    
    echo -e "${BLUE}🔍 测试详细信息 (ID: $test_id)${NC}"
    echo "================================"
    
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    id as 'ID',
    datetime(timestamp, 'localtime') as '测试时间',
    test_type as '测试类型',
    server_ip as '服务器IP',
    duration as '测试时长(秒)',
    CASE 
        WHEN test_type = 'TCP' THEN 
            'TCP带宽: ' || printf('%.2f Mbps', tcp_bandwidth_mbps) || 
            ', 重传: ' || COALESCE(tcp_retransmits, 0) || ' 次'
        WHEN test_type = 'UDP' THEN 
            'UDP带宽: ' || printf('%.2f Mbps', udp_bandwidth_mbps) || 
            ', 丢包: ' || printf('%.2f%%', COALESCE(udp_lost_percent, 0)) ||
            ', 抖动: ' || printf('%.2f ms', COALESCE(udp_jitter_ms, 0))
        ELSE 'N/A'
    END as '测试结果',
    status as '状态',
    COALESCE(error_message, '无') as '错误信息'
FROM test_results 
WHERE id = $test_id;
EOF
}

# 导出数据
export_data() {
    local format=${1:-csv}
    local filename="iperf3_results_$(date +%Y%m%d_%H%M%S).$format"
    
    echo -e "${BLUE}📤 导出数据到 $filename${NC}"
    
    if [ "$format" = "csv" ]; then
        sqlite3 -header -csv "$DB_FILE" "SELECT * FROM test_results ORDER BY timestamp DESC;" > "$filename"
    elif [ "$format" = "json" ]; then
        sqlite3 "$DB_FILE" <<EOF > "$filename"
.mode json
SELECT * FROM test_results ORDER BY timestamp DESC;
EOF
    else
        echo -e "${RED}错误: 不支持的格式 $format${NC}"
        echo "支持的格式: csv, json"
        return 1
    fi
    
    echo -e "${GREEN}✅ 数据已导出到 $filename${NC}"
}

# 清理旧数据
cleanup_old_data() {
    local days=${1:-30}
    
    echo -e "${YELLOW}🧹 清理 $days 天前的数据...${NC}"
    
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM test_results WHERE timestamp < datetime('now', '-$days days');")
    
    if [ "$count" -gt 0 ]; then
        sqlite3 "$DB_FILE" "DELETE FROM test_results WHERE timestamp < datetime('now', '-$days days');"
        sqlite3 "$DB_FILE" "VACUUM;"
        echo -e "${GREEN}✅ 已清理 $count 条旧记录${NC}"
    else
        echo -e "${GREEN}✅ 没有需要清理的数据${NC}"
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}📖 iperf3结果查看工具${NC}"
    echo "================================"
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  recent [数量]     显示最近的测试结果 (默认10条)"
    echo "  stats            显示统计信息"
    echo "  today            显示今日测试结果"
    echo "  detail <ID>      显示指定测试的详细信息"
    echo "  export [格式]    导出数据 (csv/json, 默认csv)"
    echo "  cleanup [天数]   清理旧数据 (默认30天)"
    echo "  help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 recent 20     # 显示最近20次测试"
    echo "  $0 detail 5      # 显示ID为5的测试详情"
    echo "  $0 export json   # 导出为JSON格式"
    echo "  $0 cleanup 7     # 清理7天前的数据"
}

# 主函数
main() {
    check_database
    
    local command=${1:-recent}
    
    case $command in
        "recent")
            show_recent ${2:-10}
            ;;
        "stats")
            show_statistics
            ;;
        "today")
            show_today
            ;;
        "detail")
            show_detail $2
            ;;
        "export")
            export_data ${2:-csv}
            ;;
        "cleanup")
            cleanup_old_data ${2:-30}
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$command'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
