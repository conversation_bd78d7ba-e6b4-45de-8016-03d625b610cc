#!/bin/bash

# iperf3 简化测试脚本 - OpenWRT端
# 功能：自动管理服务器iperf3，执行测试，存储结果到SQLite

# 配置
SERVER_IP="*************"        # 服务器IP
SERVER_API_PORT="8080"           # 服务器API端口
IPERF3_PORT="5201"              # iperf3测试端口
DB_FILE="./iperf3_results.db"   # SQLite数据库文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 初始化数据库
init_database() {
    log_info "初始化SQLite数据库..."
    
    sqlite3 "$DB_FILE" <<EOF
CREATE TABLE IF NOT EXISTS test_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    test_type TEXT NOT NULL,
    server_ip TEXT NOT NULL,
    duration INTEGER NOT NULL,
    
    -- TCP测试结果
    tcp_bandwidth_bps INTEGER,
    tcp_bandwidth_mbps REAL,
    tcp_retransmits INTEGER,
    tcp_cwnd INTEGER,
    
    -- UDP测试结果
    udp_bandwidth_bps INTEGER,
    udp_bandwidth_mbps REAL,
    udp_jitter_ms REAL,
    udp_lost_packets INTEGER,
    udp_total_packets INTEGER,
    udp_lost_percent REAL,
    
    -- 原始JSON数据
    raw_json TEXT,
    
    -- 测试状态
    status TEXT DEFAULT 'completed',
    error_message TEXT
);

CREATE INDEX IF NOT EXISTS idx_timestamp ON test_results(timestamp);
CREATE INDEX IF NOT EXISTS idx_test_type ON test_results(test_type);
EOF

    if [ $? -eq 0 ]; then
        log_success "数据库初始化完成"
    else
        log_error "数据库初始化失败"
        exit 1
    fi
}

# 检查服务器API连接
check_server_api() {
    log_info "检查服务器API连接..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/api_response.json "http://${SERVER_IP}:${SERVER_API_PORT}/status" 2>/dev/null)
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "服务器API连接正常"
        return 0
    else
        log_error "无法连接到服务器API (HTTP: $http_code)"
        return 1
    fi
}

# 启动服务器iperf3
start_server_iperf3() {
    log_info "启动服务器iperf3服务..."
    
    response=$(curl -s -X POST "http://${SERVER_IP}:${SERVER_API_PORT}/start" 2>/dev/null)
    success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2)
    
    if [ "$success" = "true" ]; then
        log_success "服务器iperf3启动成功"
        sleep 2  # 等待服务完全启动
        return 0
    else
        message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
        log_warning "服务器iperf3启动响应: $message"
        return 0  # 可能已经在运行，继续执行
    fi
}

# 停止服务器iperf3
stop_server_iperf3() {
    log_info "停止服务器iperf3服务..."
    
    response=$(curl -s -X POST "http://${SERVER_IP}:${SERVER_API_PORT}/stop" 2>/dev/null)
    success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2)
    
    if [ "$success" = "true" ]; then
        log_success "服务器iperf3停止成功"
    else
        message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
        log_warning "服务器iperf3停止响应: $message"
    fi
}

# 执行TCP测试
run_tcp_test() {
    log_info "开始TCP速度测试..."
    
    local json_file="/tmp/tcp_result_$(date +%s).json"
    
    if iperf3 -c "$SERVER_IP" -p "$IPERF3_PORT" -t 30 -J > "$json_file" 2>/dev/null; then
        log_success "TCP测试完成"
        
        # 解析JSON结果
        local bandwidth_bps=$(cat "$json_file" | grep -o '"bits_per_second":[0-9.]*' | tail -1 | cut -d':' -f2)
        local bandwidth_mbps=$(echo "scale=2; $bandwidth_bps / 1000000" | bc)
        local retransmits=$(cat "$json_file" | grep -o '"retransmits":[0-9]*' | cut -d':' -f2)
        local raw_json=$(cat "$json_file" | tr '\n' ' ' | sed "s/'/\'\'/g")
        
        # 存储到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    test_type, server_ip, duration,
    tcp_bandwidth_bps, tcp_bandwidth_mbps, tcp_retransmits,
    raw_json, status
) VALUES (
    'TCP', '$SERVER_IP', 30,
    $bandwidth_bps, $bandwidth_mbps, ${retransmits:-0},
    '$raw_json', 'completed'
);
EOF
        
        log_success "TCP测试结果已保存 - 带宽: ${bandwidth_mbps} Mbps, 重传: ${retransmits:-0}"
        rm -f "$json_file"
        return 0
    else
        log_error "TCP测试失败"
        
        # 记录错误到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    test_type, server_ip, duration, status, error_message
) VALUES (
    'TCP', '$SERVER_IP', 30, 'failed', 'iperf3 TCP test failed'
);
EOF
        return 1
    fi
}

# 执行UDP测试
run_udp_test() {
    log_info "开始UDP质量测试..."
    
    local json_file="/tmp/udp_result_$(date +%s).json"
    
    if iperf3 -c "$SERVER_IP" -p "$IPERF3_PORT" -u -b 100M -t 30 -J > "$json_file" 2>/dev/null; then
        log_success "UDP测试完成"
        
        # 解析JSON结果
        local bandwidth_bps=$(cat "$json_file" | grep -o '"bits_per_second":[0-9.]*' | tail -1 | cut -d':' -f2)
        local bandwidth_mbps=$(echo "scale=2; $bandwidth_bps / 1000000" | bc)
        local jitter=$(cat "$json_file" | grep -o '"jitter_ms":[0-9.]*' | cut -d':' -f2)
        local lost_packets=$(cat "$json_file" | grep -o '"lost_packets":[0-9]*' | cut -d':' -f2)
        local total_packets=$(cat "$json_file" | grep -o '"packets":[0-9]*' | cut -d':' -f2)
        local lost_percent=$(cat "$json_file" | grep -o '"lost_percent":[0-9.]*' | cut -d':' -f2)
        local raw_json=$(cat "$json_file" | tr '\n' ' ' | sed "s/'/\'\'/g")
        
        # 存储到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    test_type, server_ip, duration,
    udp_bandwidth_bps, udp_bandwidth_mbps, udp_jitter_ms,
    udp_lost_packets, udp_total_packets, udp_lost_percent,
    raw_json, status
) VALUES (
    'UDP', '$SERVER_IP', 30,
    $bandwidth_bps, $bandwidth_mbps, ${jitter:-0},
    ${lost_packets:-0}, ${total_packets:-0}, ${lost_percent:-0},
    '$raw_json', 'completed'
);
EOF
        
        log_success "UDP测试结果已保存 - 带宽: ${bandwidth_mbps} Mbps, 丢包率: ${lost_percent:-0}%, 抖动: ${jitter:-0} ms"
        rm -f "$json_file"
        return 0
    else
        log_error "UDP测试失败"
        
        # 记录错误到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    test_type, server_ip, duration, status, error_message
) VALUES (
    'UDP', '$SERVER_IP', 30, 'failed', 'iperf3 UDP test failed'
);
EOF
        return 1
    fi
}

# 显示最近的测试结果
show_recent_results() {
    log_info "最近5次测试结果："
    
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    datetime(timestamp, 'localtime') as '测试时间',
    test_type as '类型',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%.2f Mbps', tcp_bandwidth_mbps)
        WHEN test_type = 'UDP' THEN printf('%.2f Mbps', udp_bandwidth_mbps)
    END as '带宽',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%d 次', tcp_retransmits)
        WHEN test_type = 'UDP' THEN printf('%.2f%%', udp_lost_percent)
    END as '重传/丢包',
    status as '状态'
FROM test_results 
ORDER BY timestamp DESC 
LIMIT 5;
EOF
}

# 主函数
main() {
    echo "🚀 iperf3 简化测试脚本启动"
    echo "================================"
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 初始化数据库
    init_database
    
    # 检查服务器API
    if ! check_server_api; then
        log_error "无法连接到服务器，请检查服务器是否启动"
        exit 1
    fi
    
    # 启动服务器iperf3
    start_server_iperf3
    
    # 执行测试
    tcp_success=false
    udp_success=false
    
    if run_tcp_test; then
        tcp_success=true
    fi
    
    sleep 2  # 测试间隔
    
    if run_udp_test; then
        udp_success=true
    fi
    
    # 停止服务器iperf3
    stop_server_iperf3
    
    # 显示结果
    echo ""
    show_recent_results
    
    # 总结
    echo ""
    echo "================================"
    if [ "$tcp_success" = true ] && [ "$udp_success" = true ]; then
        log_success "所有测试完成！"
        exit 0
    elif [ "$tcp_success" = true ] || [ "$udp_success" = true ]; then
        log_warning "部分测试完成"
        exit 1
    else
        log_error "所有测试失败"
        exit 2
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
