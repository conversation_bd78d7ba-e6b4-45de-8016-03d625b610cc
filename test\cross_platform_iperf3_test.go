package test

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"testing"

	"github.com/sirupsen/logrus"
)

// PlatformConfig 平台配置结构
type PlatformConfig struct {
	OS         string
	BinaryPath string
	PidFile    string
	LogFile    string
	UseDaemon  bool
	UseSystemd bool
}

// TestCrossPlatformIperf3 测试跨平台iperf3命令支持
func TestCrossPlatformIperf3(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("平台检测和iperf3路径", func(t *testing.T) {
		currentOS := runtime.GOOS
		currentArch := runtime.GOARCH

		t.Logf("当前运行平台: %s/%s", currentOS, currentArch)

		t.Run("Windows开发环境", func(t *testing.T) {
			if currentOS != "windows" {
				t.Skip("跳过Windows特定测试")
			}

			// Windows环境下的iperf3路径
			windowsPaths := []string{
				"iperf3.exe",                            // 项目根目录
				"./iperf3.exe",                          // 相对路径
				"C:\\iperf3\\iperf3.exe",                // 标准安装路径
				"C:\\Program Files\\iperf3\\iperf3.exe", // Program Files路径
			}

			foundPath := ""
			for _, path := range windowsPaths {
				if _, err := os.Stat(path); err == nil {
					foundPath = path
					break
				}
			}

			if foundPath == "" {
				t.Log("⚠️ Windows环境下未找到iperf3.exe，将使用默认路径")
				foundPath = "iperf3.exe"
			}

			t.Logf("✅ Windows iperf3路径: %s", foundPath)

			// 验证Windows命令格式
			windowsCommands := []struct {
				name    string
				command string
			}{
				{"TCP客户端", fmt.Sprintf("%s -c 127.0.0.1 -p 5201 -t 10 -J", foundPath)},
				{"UDP客户端", fmt.Sprintf("%s -c 127.0.0.1 -p 5201 -u -b 100M -t 10 -J", foundPath)},
				{"TCP服务端", fmt.Sprintf("%s -s -p 5201 -D", foundPath)},
				{"UDP服务端", fmt.Sprintf("%s -s -p 5201 -D", foundPath)},
			}

			for _, cmd := range windowsCommands {
				if !strings.Contains(cmd.command, foundPath) {
					t.Errorf("Windows命令格式错误: %s", cmd.command)
				}
				t.Logf("✅ %s: %s", cmd.name, cmd.command)
			}
		})

		t.Run("OpenWRT部署环境", func(t *testing.T) {
			// OpenWRT环境下的iperf3路径和命令
			openwrtPaths := []string{
				"/usr/bin/iperf3",  // 标准路径
				"/usr/sbin/iperf3", // 系统路径
				"/opt/bin/iperf3",  // 可选路径
			}
			_ = openwrtPaths // 标记为已使用

			// 模拟OpenWRT环境检测
			openwrtPath := "/usr/bin/iperf3" // 默认路径
			t.Logf("✅ OpenWRT iperf3路径: %s", openwrtPath)

			// 验证OpenWRT命令格式
			openwrtCommands := []struct {
				name    string
				command string
			}{
				{"TCP客户端", fmt.Sprintf("%s -c ************* -p 5201 -t 30 -J", openwrtPath)},
				{"UDP客户端", fmt.Sprintf("%s -c ************* -p 5201 -u -b 500M -t 30 -J", openwrtPath)},
				{"TCP服务端", fmt.Sprintf("%s -s -p 5201 -D --pidfile /var/run/iperf3.pid", openwrtPath)},
				{"UDP服务端", fmt.Sprintf("%s -s -p 5201 -D --pidfile /var/run/iperf3.pid", openwrtPath)},
			}

			for _, cmd := range openwrtCommands {
				if !strings.Contains(cmd.command, openwrtPath) {
					t.Errorf("OpenWRT命令格式错误: %s", cmd.command)
				}
				t.Logf("✅ %s: %s", cmd.name, cmd.command)
			}

			// 验证OpenWRT特有参数
			openwrtSpecific := []string{
				"--pidfile /var/run/iperf3.pid", // PID文件
				"-D",                            // 守护进程模式
				"--logfile /var/log/iperf3.log", // 日志文件
			}

			for _, param := range openwrtSpecific {
				t.Logf("✅ OpenWRT特有参数: %s", param)
			}
		})

		t.Run("Debian部署环境", func(t *testing.T) {
			// Debian环境下的iperf3路径和命令
			debianPaths := []string{
				"/usr/bin/iperf3",       // 标准路径
				"/usr/local/bin/iperf3", // 本地安装路径
			}
			_ = debianPaths // 标记为已使用

			debianPath := "/usr/bin/iperf3" // 默认路径
			t.Logf("✅ Debian iperf3路径: %s", debianPath)

			// 验证Debian命令格式
			debianCommands := []struct {
				name    string
				command string
			}{
				{"TCP客户端", fmt.Sprintf("%s -c ************* -p 5201 -t 30 -J", debianPath)},
				{"UDP客户端", fmt.Sprintf("%s -c ************* -p 5201 -u -b 500M -t 30 -J", debianPath)},
				{"TCP服务端", "systemctl start iperf3@5201"}, // 使用systemd
				{"UDP服务端", "systemctl start iperf3@5202"}, // 使用systemd
			}

			for _, cmd := range debianCommands {
				t.Logf("✅ %s: %s", cmd.name, cmd.command)
			}

			// 验证Debian特有功能
			debianSpecific := []string{
				"systemctl enable iperf3@5201", // 开机自启
				"systemctl status iperf3@5201", // 状态检查
				"journalctl -u iperf3@5201",    // 日志查看
			}

			for _, cmd := range debianSpecific {
				t.Logf("✅ Debian特有命令: %s", cmd)
			}
		})
	})

	t.Run("跨平台命令生成器", func(t *testing.T) {
		// 使用全局定义的PlatformConfig
		platforms := []PlatformConfig{
			{
				OS:         "windows",
				BinaryPath: "iperf3.exe",
				UseDaemon:  false,
				UseSystemd: false,
			},
			{
				OS:         "openwrt",
				BinaryPath: "/usr/bin/iperf3",
				PidFile:    "/var/run/iperf3.pid",
				LogFile:    "/var/log/iperf3.log",
				UseDaemon:  true,
				UseSystemd: false,
			},
			{
				OS:         "debian",
				BinaryPath: "/usr/bin/iperf3",
				PidFile:    "/var/run/iperf3.pid",
				LogFile:    "/var/log/iperf3.log",
				UseDaemon:  true,
				UseSystemd: true,
			},
		}

		for _, platform := range platforms {
			t.Run(fmt.Sprintf("平台_%s", platform.OS), func(t *testing.T) {
				// 生成TCP客户端命令
				tcpClientCmd := generateIperf3Command(platform, "client", "tcp", "*************", 5201, 30)
				if tcpClientCmd == "" {
					t.Errorf("平台 %s TCP客户端命令生成失败", platform.OS)
				}
				t.Logf("✅ %s TCP客户端: %s", platform.OS, tcpClientCmd)

				// 生成UDP客户端命令
				udpClientCmd := generateIperf3Command(platform, "client", "udp", "*************", 5201, 30)
				if udpClientCmd == "" {
					t.Errorf("平台 %s UDP客户端命令生成失败", platform.OS)
				}
				t.Logf("✅ %s UDP客户端: %s", platform.OS, udpClientCmd)

				// 生成服务端命令
				serverCmd := generateIperf3Command(platform, "server", "tcp", "", 5201, 0)
				if serverCmd == "" {
					t.Errorf("平台 %s 服务端命令生成失败", platform.OS)
				}
				t.Logf("✅ %s 服务端: %s", platform.OS, serverCmd)
			})
		}
	})

	t.Run("配置文件适配", func(t *testing.T) {
		// 测试不同平台的配置文件适配
		configTemplates := map[string]map[string]string{
			"windows": {
				"binary_path": "iperf3.exe",
				"config_dir":  "./configs",
				"log_dir":     "./logs",
				"data_dir":    "./data",
				"temp_dir":    "./temp",
			},
			"openwrt": {
				"binary_path": "/usr/bin/iperf3",
				"config_dir":  "/etc/iperf3-controller",
				"log_dir":     "/var/log",
				"data_dir":    "/var/lib/iperf3-controller",
				"temp_dir":    "/tmp",
			},
			"debian": {
				"binary_path": "/usr/bin/iperf3",
				"config_dir":  "/etc/iperf3-controller",
				"log_dir":     "/var/log/iperf3-controller",
				"data_dir":    "/var/lib/iperf3-controller",
				"temp_dir":    "/tmp",
			},
		}

		for platform, config := range configTemplates {
			t.Run(fmt.Sprintf("配置_%s", platform), func(t *testing.T) {
				// 验证配置完整性
				requiredKeys := []string{"binary_path", "config_dir", "log_dir", "data_dir", "temp_dir"}
				for _, key := range requiredKeys {
					if config[key] == "" {
						t.Errorf("平台 %s 缺少配置项: %s", platform, key)
					}
				}

				// 验证路径格式
				binaryPath := config["binary_path"]
				if platform == "windows" {
					if !strings.HasSuffix(binaryPath, ".exe") {
						t.Errorf("Windows平台二进制文件应以.exe结尾: %s", binaryPath)
					}
				} else {
					if strings.HasSuffix(binaryPath, ".exe") {
						t.Errorf("非Windows平台二进制文件不应以.exe结尾: %s", binaryPath)
					}
				}

				t.Logf("✅ %s 配置验证通过:", platform)
				for key, value := range config {
					t.Logf("   %s: %s", key, value)
				}
			})
		}
	})

	t.Run("环境检测和自适应", func(t *testing.T) {
		// 模拟环境检测逻辑
		detectedEnv := detectEnvironment()
		t.Logf("检测到的环境: %s", detectedEnv)

		// 根据环境选择合适的配置
		envConfigs := map[string]map[string]interface{}{
			"windows_dev": {
				"iperf3_path":     "iperf3.exe",
				"max_concurrent":  2,
				"test_timeout":    "30s",
				"use_json_output": true,
			},
			"openwrt_prod": {
				"iperf3_path":     "/usr/bin/iperf3",
				"max_concurrent":  4,
				"test_timeout":    "60s",
				"use_json_output": true,
				"use_daemon":      true,
			},
			"debian_prod": {
				"iperf3_path":     "/usr/bin/iperf3",
				"max_concurrent":  6,
				"test_timeout":    "60s",
				"use_json_output": true,
				"use_systemd":     true,
			},
		}

		selectedConfig := envConfigs[detectedEnv]
		if selectedConfig == nil {
			selectedConfig = envConfigs["windows_dev"] // 默认配置
		}

		t.Logf("✅ 选择的配置:")
		for key, value := range selectedConfig {
			t.Logf("   %s: %v", key, value)
		}

		// 验证配置有效性
		if selectedConfig["iperf3_path"] == "" {
			t.Error("iperf3路径不能为空")
		}
		if selectedConfig["max_concurrent"].(int) <= 0 {
			t.Error("最大并发数必须大于0")
		}
	})
}

// generateIperf3Command 生成跨平台的iperf3命令
func generateIperf3Command(platform PlatformConfig, mode, protocol, host string, port, duration int) string {
	var cmd strings.Builder

	if mode == "server" {
		if platform.UseSystemd {
			// Debian使用systemd
			return fmt.Sprintf("systemctl start iperf3@%d", port)
		} else {
			// 直接启动服务端
			cmd.WriteString(platform.BinaryPath)
			cmd.WriteString(" -s")
			cmd.WriteString(fmt.Sprintf(" -p %d", port))

			if platform.UseDaemon {
				cmd.WriteString(" -D")
				if platform.PidFile != "" {
					cmd.WriteString(fmt.Sprintf(" --pidfile %s", platform.PidFile))
				}
			}
		}
	} else {
		// 客户端模式
		cmd.WriteString(platform.BinaryPath)
		cmd.WriteString(fmt.Sprintf(" -c %s", host))
		cmd.WriteString(fmt.Sprintf(" -p %d", port))
		cmd.WriteString(fmt.Sprintf(" -t %d", duration))
		cmd.WriteString(" -J") // JSON输出

		if protocol == "udp" {
			cmd.WriteString(" -u -b 500M")
		}
	}

	return cmd.String()
}

// detectEnvironment 检测当前运行环境
func detectEnvironment() string {
	currentOS := runtime.GOOS

	switch currentOS {
	case "windows":
		return "windows_dev"
	case "linux":
		// 检测是否为OpenWRT
		if _, err := os.Stat("/etc/openwrt_release"); err == nil {
			return "openwrt_prod"
		}
		// 检测是否为Debian
		if _, err := os.Stat("/etc/debian_version"); err == nil {
			return "debian_prod"
		}
		return "linux_unknown"
	default:
		return "unknown"
	}
}
