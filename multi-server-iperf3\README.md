# 🌐 多服务器iperf3网络质量监控系统

一个专为OpenWRT设计的多服务器网络性能监控解决方案，支持同时测试12台服务器的网络质量，并提供实时Web可视化界面。

## 🎯 系统架构

```
OpenWRT (监控中心)                    12台服务器
     |                               |
     |-- 测试脚本 -----------------> Server1 (iperf3 -s)
     |-- SQLite数据库                Server2 (iperf3 -s)  
     |-- Web可视化界面               Server3 (iperf3 -s)
     |-- Cron定时任务                ...
     |-- 并发测试控制                Server12 (iperf3 -s)
```

## ✨ 核心功能

### 🚀 OpenWRT端功能
- ✅ **多服务器并发测试**: 同时测试12台服务器，支持并发控制
- ✅ **TCP/UDP双重测试**: 全面评估网络速度和质量
- ✅ **SQLite本地存储**: 完整的测试数据存储和管理
- ✅ **Web可视化界面**: 实时图表展示，支持历史数据分析
- ✅ **智能告警系统**: 自动检测异常并告警
- ✅ **定时自动测试**: 支持cron定时执行
- ✅ **数据导出功能**: 支持CSV/JSON格式导出

### 🖥️ 服务器端功能
- ✅ **一键启动脚本**: 简单的iperf3服务器管理
- ✅ **进程监控**: 自动检测和管理iperf3进程
- ✅ **日志记录**: 完整的运行日志
- ✅ **状态检查**: 实时状态监控和测试

### 📊 Web界面功能
- ✅ **实时监控面板**: 显示所有服务器状态
- ✅ **性能趋势图**: 历史数据可视化
- ✅ **详细统计**: 各服务器性能对比
- ✅ **响应式设计**: 支持手机、平板访问
- ✅ **自动刷新**: 30秒自动更新数据

## 📁 项目结构

```
multi-server-iperf3/
├── openwrt/                    # OpenWRT端文件
│   ├── config.conf            # 配置文件
│   ├── multi_server_test.sh   # 主测试脚本
│   └── web/                   # Web界面
│       ├── index.html         # 主页面
│       └── app.js             # JavaScript应用
├── servers/                   # 服务器端文件
│   └── start_iperf3_server.sh # 服务器启动脚本
└── README.md                  # 说明文档
```

## 🚀 快速部署

### 1. 服务器端部署 (12台服务器)

在每台服务器上执行：

```bash
# 下载脚本
wget https://your-repo/start_iperf3_server.sh
chmod +x start_iperf3_server.sh

# 启动iperf3服务
./start_iperf3_server.sh start

# 检查状态
./start_iperf3_server.sh status

# 设置开机自启 (可选)
echo "@reboot /path/to/start_iperf3_server.sh start" | crontab -
```

### 2. OpenWRT端部署

```bash
# 上传文件到OpenWRT
scp -r openwrt/ root@openwrt-ip:/root/iperf3-monitor/

# 登录OpenWRT
ssh root@openwrt-ip

# 进入目录
cd /root/iperf3-monitor

# 设置执行权限
chmod +x multi_server_test.sh

# 安装依赖
opkg update
opkg install iperf3 sqlite3-cli bc curl

# 编辑配置文件
vi config.conf
```

### 3. 配置服务器列表

编辑 `config.conf` 文件，修改服务器信息：

```bash
SERVERS=(
    "Server-01|*************|5201|主服务器|true"
    "Server-02|*************|5201|备用服务器|true"
    # ... 添加您的12台服务器信息
)
```

### 4. 首次运行测试

```bash
# 执行测试
./multi_server_test.sh test

# 启动Web服务器
./multi_server_test.sh web

# 访问Web界面
# http://openwrt-ip:8080
```

## ⏰ 设置定时任务

### 每小时执行测试

```bash
# 编辑crontab
crontab -e

# 添加定时任务
0 * * * * /root/iperf3-monitor/multi_server_test.sh test >> /var/log/iperf3_cron.log 2>&1
```

### 常用定时设置

```bash
# 每30分钟执行
*/30 * * * * /root/iperf3-monitor/multi_server_test.sh test

# 每天凌晨2点执行
0 2 * * * /root/iperf3-monitor/multi_server_test.sh test

# 工作日每小时执行
0 * * * 1-5 /root/iperf3-monitor/multi_server_test.sh test
```

## 📊 Web界面使用

### 访问界面
- 地址: `http://openwrt-ip:8080`
- 支持手机、平板访问
- 自动30秒刷新数据

### 主要功能
1. **实时监控**: 显示所有服务器在线状态和性能
2. **趋势图表**: 查看历史性能趋势
3. **详细统计**: 各服务器性能对比分析
4. **数据导出**: 导出测试数据为JSON格式
5. **时间范围**: 支持1小时、6小时、24小时、7天数据查看

### 状态指示
- 🟢 绿色: 服务器在线，性能良好
- 🟡 黄色: 服务器在线，性能警告
- 🔴 红色: 服务器离线或性能异常

## 🔧 配置说明

### 主要配置项

```bash
# 数据库配置
DB_FILE="./iperf3_results.db"

# Web服务配置
WEB_PORT=8080

# 测试配置
TEST_DURATION=30              # 测试时长(秒)
TCP_PARALLEL_STREAMS=4        # TCP并行流数
UDP_BANDWIDTH="100M"          # UDP测试带宽
MAX_CONCURRENT_TESTS=3        # 最大并发测试数

# 告警配置
ALERT_TCP_MIN_SPEED=100       # TCP最低速度告警(Mbps)
ALERT_UDP_MAX_LOSS=5          # UDP最大丢包率告警(%)

# 数据保留
DATA_RETENTION_DAYS=90        # 数据保留天数
```

### 服务器配置格式

```bash
"服务器名称|IP地址|端口|描述|是否启用"
```

## 📈 数据库结构

### 主要表结构

```sql
-- 服务器信息表
CREATE TABLE servers (
    id INTEGER PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    ip_address TEXT NOT NULL,
    port INTEGER NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT 1
);

-- 测试结果表
CREATE TABLE test_results (
    id INTEGER PRIMARY KEY,
    server_id INTEGER NOT NULL,
    server_name TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    test_type TEXT NOT NULL,
    
    -- TCP结果
    tcp_bandwidth_mbps REAL,
    tcp_retransmits INTEGER,
    
    -- UDP结果
    udp_bandwidth_mbps REAL,
    udp_lost_percent REAL,
    udp_jitter_ms REAL,
    
    -- 状态
    status TEXT DEFAULT 'completed',
    raw_json TEXT
);
```

## 🛠️ 故障排除

### 常见问题

1. **测试失败**
   ```bash
   # 检查服务器连通性
   ping server-ip
   
   # 检查端口
   telnet server-ip 5201
   
   # 查看日志
   tail -f iperf3_test.log
   ```

2. **Web界面无法访问**
   ```bash
   # 检查Web服务器状态
   ps aux | grep python3
   
   # 重启Web服务器
   ./multi_server_test.sh stop-web
   ./multi_server_test.sh web
   ```

3. **数据库问题**
   ```bash
   # 检查数据库文件
   ls -la iperf3_results.db
   
   # 测试数据库连接
   sqlite3 iperf3_results.db "SELECT COUNT(*) FROM test_results;"
   ```

### 性能优化

1. **并发控制**: 调整 `MAX_CONCURRENT_TESTS` 参数
2. **测试时长**: 根据网络情况调整 `TEST_DURATION`
3. **数据清理**: 定期清理旧数据，保持数据库性能

## 📊 监控指标

### TCP测试指标
- **带宽**: 网络传输速度 (Mbps)
- **重传次数**: 数据包重传统计
- **连接时间**: 建立连接耗时 (ms)

### UDP测试指标
- **带宽**: 网络传输速度 (Mbps)
- **丢包率**: 数据包丢失百分比 (%)
- **抖动**: 网络延迟变化 (ms)

### 系统指标
- **在线率**: 服务器可用性统计
- **测试成功率**: 测试执行成功比例
- **平均性能**: 所有服务器平均性能

## 🔄 扩展功能

### 可选扩展
1. **邮件告警**: 集成邮件通知功能
2. **微信告警**: 集成企业微信机器人
3. **更多图表**: 添加更详细的性能分析图表
4. **API接口**: 提供RESTful API供其他系统调用
5. **移动应用**: 开发专用的移动监控应用

### 集成建议
- **Grafana**: 集成Grafana进行高级数据可视化
- **Prometheus**: 集成Prometheus进行指标收集
- **ELK Stack**: 集成ELK进行日志分析

## 📄 许可证

MIT License - 自由使用和修改

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**🎉 现在您就拥有了一个完整的多服务器网络质量监控系统！**
