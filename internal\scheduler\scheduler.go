package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/database"

	"github.com/sirupsen/logrus"
)

// ScheduleMode represents different scheduling modes
type ScheduleMode string

const (
	// ScheduleModeOdd schedules tests on odd hours (1,3,5,7,9,11,13,15,17,19,21,23)
	ScheduleModeOdd ScheduleMode = "odd"

	// ScheduleModeEven schedules tests on even hours (0,2,4,6,8,10,12,14,16,18,20,22)
	ScheduleModeEven ScheduleMode = "even"

	// ScheduleModeAlways schedules tests every hour
	ScheduleModeAlways ScheduleMode = "always"
)

// Scheduler manages the scheduling of iperf3 tests
type Scheduler interface {
	// Start starts the scheduler
	Start(ctx context.Context) error

	// Stop stops the scheduler
	Stop() error

	// TriggerTest manually triggers a test for the current hour
	TriggerTest(ctx context.Context) error

	// GetStatus returns the current scheduler status
	GetStatus() *SchedulerStatus

	// GetNextScheduledTime returns the next scheduled test time
	GetNextScheduledTime() time.Time
}

// SchedulerStatus represents the current status of the scheduler
type SchedulerStatus struct {
	IsRunning       bool       `json:"is_running"`
	Mode            string     `json:"mode"`
	Timezone        string     `json:"timezone"`
	LastTestTime    *time.Time `json:"last_test_time,omitempty"`
	NextTestTime    *time.Time `json:"next_test_time,omitempty"`
	TotalTests      int        `json:"total_tests"`
	SuccessfulTests int        `json:"successful_tests"`
	FailedTests     int        `json:"failed_tests"`
	LastError       *string    `json:"last_error,omitempty"`
}

// TestExecution represents a test execution record
type TestExecution struct {
	ID        string     `json:"id"`
	Hour      int        `json:"hour"`
	StartTime time.Time  `json:"start_time"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	Status    string     `json:"status"` // "running", "completed", "failed"
	Error     *string    `json:"error,omitempty"`
	Triggered string     `json:"triggered"` // "scheduled", "manual"
}

// DefaultScheduler implements the Scheduler interface
type DefaultScheduler struct {
	config      *config.ScheduleConfig
	coordinator coordinator.TestCoordinator
	repository  *database.Repository
	logger      *logrus.Logger
	timezone    *time.Location

	// State management
	isRunning bool
	stopChan  chan struct{}
	ticker    *time.Ticker
	mu        sync.RWMutex

	// Statistics
	totalTests      int
	successfulTests int
	failedTests     int
	lastTestTime    *time.Time
	lastError       error

	// Current execution
	currentExecution *TestExecution
}

// NewScheduler creates a new scheduler
func NewScheduler(
	config *config.ScheduleConfig,
	coordinator coordinator.TestCoordinator,
	repository *database.Repository,
	logger *logrus.Logger,
) (Scheduler, error) {
	if logger == nil {
		logger = logrus.New()
	}

	// Validate mode
	validModes := []string{"odd", "even", "always"}
	isValidMode := false
	for _, mode := range validModes {
		if config.Mode == mode {
			isValidMode = true
			break
		}
	}
	if !isValidMode {
		return nil, fmt.Errorf("invalid schedule mode: %s, must be one of: odd, even, always", config.Mode)
	}

	// Parse timezone
	timezone, err := time.LoadLocation(config.Timezone)
	if err != nil {
		return nil, fmt.Errorf("invalid timezone %s: %w", config.Timezone, err)
	}

	return &DefaultScheduler{
		config:      config,
		coordinator: coordinator,
		repository:  repository,
		logger:      logger,
		timezone:    timezone,
		stopChan:    make(chan struct{}),
	}, nil
}

// Start starts the scheduler
func (s *DefaultScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	s.logger.WithFields(logrus.Fields{
		"mode":     s.config.Mode,
		"timezone": s.config.Timezone,
	}).Info("Starting scheduler")

	s.isRunning = true

	// Calculate next test time
	nextTime := s.calculateNextTestTime()
	s.logger.WithField("next_test_time", nextTime).Info("Next test scheduled")

	// Start the scheduling routine
	go s.scheduleRoutine(ctx)

	s.logger.Info("Scheduler started successfully")
	return nil
}

// Stop stops the scheduler
func (s *DefaultScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return nil // 已经停止，直接返回成功
	}

	s.logger.Info("Stopping scheduler")

	s.isRunning = false

	// 安全关闭channel
	select {
	case <-s.stopChan:
		// channel已经关闭
	default:
		close(s.stopChan)
	}

	if s.ticker != nil {
		s.ticker.Stop()
	}

	s.logger.Info("Scheduler stopped")
	return nil
}

// GetStatus returns the current scheduler status
func (s *DefaultScheduler) GetStatus() *SchedulerStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	status := &SchedulerStatus{
		IsRunning:       s.isRunning,
		Mode:            s.config.Mode,
		Timezone:        s.config.Timezone,
		TotalTests:      s.totalTests,
		SuccessfulTests: s.successfulTests,
		FailedTests:     s.failedTests,
	}

	if s.lastTestTime != nil {
		status.LastTestTime = s.lastTestTime
	}

	if s.isRunning {
		nextTime := s.calculateNextTestTime()
		status.NextTestTime = &nextTime
	}

	if s.lastError != nil {
		errStr := s.lastError.Error()
		status.LastError = &errStr
	}

	return status
}

// GetNextScheduledTime returns the next scheduled test time
func (s *DefaultScheduler) GetNextScheduledTime() time.Time {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.calculateNextTestTime()
}

// TriggerTest manually triggers a test for the current hour
func (s *DefaultScheduler) TriggerTest(ctx context.Context) error {
	s.logger.Info("Manual test trigger requested")

	now := time.Now().In(s.timezone)
	hour := now.Hour()

	return s.executeTest(ctx, hour, "manual")
}
