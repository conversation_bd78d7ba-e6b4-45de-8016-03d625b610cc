package test

import (
	"context"
	"testing"
	"time"

	"iperf3-controller/internal/sync"

	"github.com/sirupsen/logrus"
)

// TestSyncManager 测试同步管理器
func TestSyncManager(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("创建同步管理器", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		if syncManager == nil {
			t.Fatal("同步管理器创建失败")
		}

		t.Log("✅ 同步管理器创建成功")
	})

	t.Run("配置验证", func(t *testing.T) {
		// 测试有效配置
		validConfigs := []*sync.SyncConfig{
			{
				PeerHost:          "*************",
				PeerPort:          8080,
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
				EnableCompression: true,
				EnableEncryption:  false,
			},
			{
				PeerHost:          "**********",
				PeerPort:          9090,
				SyncInterval:      60 * time.Second,
				HeartbeatInterval: 20 * time.Second,
				ConnectTimeout:    10 * time.Second,
				SyncTimeout:       60 * time.Second,
				RetryAttempts:     5,
				RetryDelay:        5 * time.Second,
				EnableCompression: false,
				EnableEncryption:  true,
			},
		}

		for i, cfg := range validConfigs {
			syncManager := sync.NewSyncManager(cfg, nil, logger)
			if syncManager == nil {
				t.Fatalf("有效配置 %d 创建同步管理器失败", i+1)
			}
		}

		// 测试无效配置
		invalidConfigs := []*sync.SyncConfig{
			{
				PeerHost:          "", // 空主机
				PeerPort:          8080,
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
			},
			{
				PeerHost:          "*************",
				PeerPort:          0, // 无效端口
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
			},
			{
				PeerHost:          "*************",
				PeerPort:          8080,
				SyncInterval:      0, // 无效同步间隔
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
			},
		}

		for i, cfg := range invalidConfigs {
			syncManager := sync.NewSyncManager(cfg, nil, logger)
			if syncManager != nil {
				t.Fatalf("无效配置 %d 不应该创建成功", i+1)
			}
		}

		t.Log("✅ 配置验证通过")
	})

	t.Run("同步管理器启动和停止", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      5 * time.Second, // 缩短间隔便于测试
			HeartbeatInterval: 2 * time.Second, // 缩短间隔便于测试
			ConnectTimeout:    2 * time.Second, // 缩短超时便于测试
			SyncTimeout:       10 * time.Second,
			RetryAttempts:     2,
			RetryDelay:        1 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		ctx := context.Background()

		// 启动同步管理器
		err := syncManager.Start(ctx)
		if err != nil {
			t.Fatalf("启动同步管理器失败: %v", err)
		}

		// 检查状态
		status := syncManager.GetStatus()
		if !status.IsRunning {
			t.Fatal("同步管理器状态显示未运行")
		}

		if status.SyncInterval != 5*time.Second {
			t.Fatalf("同步间隔不匹配: 期望 %v, 实际 %v", 5*time.Second, status.SyncInterval)
		}

		t.Log("✅ 同步管理器启动成功")

		// 等待一小段时间让同步协程运行
		time.Sleep(100 * time.Millisecond)

		// 停止同步管理器
		err = syncManager.Stop()
		if err != nil {
			t.Fatalf("停止同步管理器失败: %v", err)
		}

		// 检查状态
		status = syncManager.GetStatus()
		if status.IsRunning {
			t.Fatal("同步管理器状态显示仍在运行")
		}

		t.Log("✅ 同步管理器停止成功")
	})

	t.Run("重复启动和停止", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		ctx := context.Background()

		// 启动同步管理器
		syncManager.Start(ctx)

		// 重复启动
		err := syncManager.Start(ctx)
		if err == nil {
			t.Fatal("重复启动同步管理器应该失败")
		}

		t.Log("✅ 重复启动同步管理器正确失败")

		// 停止同步管理器
		syncManager.Stop()

		// 重复停止 - 应该成功（幂等操作）
		err = syncManager.Stop()
		if err != nil {
			t.Fatalf("重复停止同步管理器失败: %v", err)
		}

		t.Log("✅ 重复停止同步管理器正确处理（幂等操作）")
	})

	t.Run("状态查询", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		ctx := context.Background()

		// 启动同步管理器
		syncManager.Start(ctx)

		status := syncManager.GetStatus()

		// 验证状态字段
		if !status.IsRunning {
			t.Fatal("同步管理器应该在运行状态")
		}

		if status.SyncInterval != 30*time.Second {
			t.Fatalf("同步间隔不匹配: 期望 %v, 实际 %v", 30*time.Second, status.SyncInterval)
		}

		if status.TotalSyncs < 0 {
			t.Fatalf("总同步数不能为负数: %d", status.TotalSyncs)
		}

		if status.SuccessfulSyncs < 0 {
			t.Fatalf("成功同步数不能为负数: %d", status.SuccessfulSyncs)
		}

		if status.FailedSyncs < 0 {
			t.Fatalf("失败同步数不能为负数: %d", status.FailedSyncs)
		}

		t.Log("✅ 状态查询正常")

		// 清理
		syncManager.Stop()
	})

	t.Run("对端状态查询", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)

		peerStatus := syncManager.GetPeerStatus()

		// 验证对端状态字段
		if peerStatus.Host != "*************" {
			t.Fatalf("对端主机不匹配: 期望 *************, 实际 %s", peerStatus.Host)
		}

		if peerStatus.Port != 8080 {
			t.Fatalf("对端端口不匹配: 期望 8080, 实际 %d", peerStatus.Port)
		}

		// 初始状态应该是未连接
		if peerStatus.Connected {
			t.Log("⚠️ 注意: 对端显示已连接，这在测试环境中可能是正常的")
		}

		t.Log("✅ 对端状态查询正常")
	})

	t.Run("手动同步", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    2 * time.Second, // 缩短超时便于测试
			SyncTimeout:       10 * time.Second,
			RetryAttempts:     1, // 减少重试次数
			RetryDelay:        1 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		ctx := context.Background()

		// 启动同步管理器
		syncManager.Start(ctx)

		// 手动触发同步
		err := syncManager.SyncNow(ctx)
		if err != nil {
			t.Log("⚠️ 手动同步失败（这在测试环境中是正常的）:", err)
		} else {
			t.Log("✅ 手动同步成功")
		}

		// 清理
		syncManager.Stop()
	})

	t.Run("压缩和加密配置", func(t *testing.T) {
		configs := []*sync.SyncConfig{
			{
				PeerHost:          "*************",
				PeerPort:          8080,
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
				EnableCompression: true,
				EnableEncryption:  false,
			},
			{
				PeerHost:          "*************",
				PeerPort:          8080,
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
				EnableCompression: false,
				EnableEncryption:  true,
			},
			{
				PeerHost:          "*************",
				PeerPort:          8080,
				SyncInterval:      30 * time.Second,
				HeartbeatInterval: 10 * time.Second,
				ConnectTimeout:    5 * time.Second,
				SyncTimeout:       30 * time.Second,
				RetryAttempts:     3,
				RetryDelay:        2 * time.Second,
				EnableCompression: true,
				EnableEncryption:  true,
			},
		}

		for i, cfg := range configs {
			syncManager := sync.NewSyncManager(cfg, nil, logger)
			if syncManager == nil {
				t.Fatalf("配置 %d 创建同步管理器失败", i+1)
			}

			ctx := context.Background()
			err := syncManager.Start(ctx)
			if err != nil {
				t.Fatalf("配置 %d 启动同步管理器失败: %v", i+1, err)
			}

			// 等待一小段时间
			time.Sleep(50 * time.Millisecond)

			syncManager.Stop()
			t.Logf("✅ 配置 %d (压缩: %t, 加密: %t) 测试通过", i+1, cfg.EnableCompression, cfg.EnableEncryption)
		}
	})

	t.Run("上下文取消测试", func(t *testing.T) {
		syncConfig := &sync.SyncConfig{
			PeerHost:          "*************",
			PeerPort:          8080,
			SyncInterval:      30 * time.Second,
			HeartbeatInterval: 10 * time.Second,
			ConnectTimeout:    5 * time.Second,
			SyncTimeout:       30 * time.Second,
			RetryAttempts:     3,
			RetryDelay:        2 * time.Second,
			EnableCompression: true,
			EnableEncryption:  false,
		}

		syncManager := sync.NewSyncManager(syncConfig, nil, logger)
		ctx, cancel := context.WithCancel(context.Background())

		// 启动同步管理器
		syncManager.Start(ctx)

		// 取消上下文
		cancel()

		// 等待一小段时间让取消生效
		time.Sleep(100 * time.Millisecond)

		// 尝试手动同步
		newCtx := context.Background()
		err := syncManager.SyncNow(newCtx)
		if err != nil {
			t.Log("✅ 上下文取消后操作正确处理")
		}

		// 清理
		syncManager.Stop()
	})
}

// BenchmarkSyncManager 同步管理器性能测试
func BenchmarkSyncManager(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	syncConfig := &sync.SyncConfig{
		PeerHost:          "*************",
		PeerPort:          8080,
		SyncInterval:      30 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		ConnectTimeout:    5 * time.Second,
		SyncTimeout:       30 * time.Second,
		RetryAttempts:     3,
		RetryDelay:        2 * time.Second,
		EnableCompression: true,
		EnableEncryption:  false,
	}

	syncManager := sync.NewSyncManager(syncConfig, nil, logger)

	b.Run("GetStatus", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			syncManager.GetStatus()
		}
	})

	b.Run("GetPeerStatus", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			syncManager.GetPeerStatus()
		}
	})
}
