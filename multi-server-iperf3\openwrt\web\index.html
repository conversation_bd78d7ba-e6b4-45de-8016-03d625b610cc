<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多服务器网络质量监控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .unit {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .good { color: #27ae60; }
        .warning { color: #f39c12; }
        .danger { color: #e74c3c; }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .server-card {
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .server-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-online { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-offline { background: #e74c3c; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .select {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            background: white;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .server-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 多服务器网络质量监控</h1>
            <p>实时监控12台服务器的网络性能和连接质量</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>在线服务器</h3>
                <div class="number good" id="onlineServers">-</div>
                <div class="unit">/ 12 台</div>
            </div>
            <div class="stat-card">
                <h3>平均TCP速度</h3>
                <div class="number" id="avgTcpSpeed">-</div>
                <div class="unit">Mbps</div>
            </div>
            <div class="stat-card">
                <h3>平均UDP速度</h3>
                <div class="number" id="avgUdpSpeed">-</div>
                <div class="unit">Mbps</div>
            </div>
            <div class="stat-card">
                <h3>平均丢包率</h3>
                <div class="number" id="avgPacketLoss">-</div>
                <div class="unit">%</div>
            </div>
            <div class="stat-card">
                <h3>最后更新</h3>
                <div class="number" id="lastUpdate" style="font-size: 1.2em;">-</div>
                <div class="unit">时间</div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📊 性能趋势图</h2>
                <div class="controls">
                    <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                    <button class="btn btn-success" onclick="exportData()">📥 导出数据</button>
                    <select class="select" id="timeRange" onchange="updateTimeRange()">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h">最近24小时</option>
                        <option value="7d">最近7天</option>
                    </select>
                    <select class="select" id="chartType" onchange="updateChartType()">
                        <option value="line">线性图</option>
                        <option value="bar">柱状图</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <div class="section">
                <h2>🖥️ 服务器状态</h2>
                <div class="server-grid" id="serverGrid">
                    <div class="loading">正在加载服务器数据...</div>
                </div>
            </div>

            <div class="section">
                <h2>📈 详细统计</h2>
                <div class="chart-container">
                    <canvas id="detailChart"></canvas>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🚀 多服务器iperf3测试系统 | 实时更新 | OpenWRT网络监控</p>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
