package test

import (
	"context"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/scheduler"
)

// TestScheduler 测试调度器
func TestScheduler(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// 创建依赖组件
	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 2 * time.Second,
		TestTimeout:    10 * time.Second,
		StopTimeout:    2 * time.Second,
		RetryAttempts:  2,
		RetryDelay:     500 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	// 添加测试客户端
	testClients := []client.ClientInfo{
		{
			ID:      "sched-client-1",
			Name:    "调度器测试客户端1",
			Host:    "*************",
			Port:    55200,
			Enabled: true,
		},
		{
			ID:      "sched-client-2",
			Name:    "调度器测试客户端2",
			Host:    "*************",
			Port:    55201,
			Enabled: true,
		},
	}

	for _, clientInfo := range testClients {
		clientManager.AddClient(clientInfo)
	}

	t.Run("创建调度器", func(t *testing.T) {
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "odd",
			Timezone: "Asia/Shanghai",
		}

		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		if testScheduler == nil {
			t.Fatal("调度器创建返回nil")
		}

		t.Log("✅ 调度器创建成功")
	})

	t.Run("调度器配置验证", func(t *testing.T) {
		// 测试有效配置
		validConfigs := []*config.ScheduleConfig{
			{Mode: "odd", Timezone: "Asia/Shanghai"},
			{Mode: "even", Timezone: "UTC"},
			{Mode: "always", Timezone: "America/New_York"},
		}

		for i, cfg := range validConfigs {
			testScheduler, err := scheduler.NewScheduler(cfg, testCoordinator, nil, logger)
			if err != nil {
				t.Fatalf("有效配置 %d 创建调度器失败: %v", i+1, err)
			}
			if testScheduler == nil {
				t.Fatalf("有效配置 %d 调度器创建返回nil", i+1)
			}
		}

		// 测试无效配置
		invalidConfigs := []*config.ScheduleConfig{
			{Mode: "invalid", Timezone: "Asia/Shanghai"},
			{Mode: "odd", Timezone: "Invalid/Timezone"},
			{Mode: "", Timezone: "Asia/Shanghai"},
		}

		for i, cfg := range invalidConfigs {
			testScheduler, err := scheduler.NewScheduler(cfg, testCoordinator, nil, logger)
			if err == nil {
				t.Fatalf("无效配置 %d 应该创建失败", i+1)
			}
			if testScheduler != nil {
				t.Fatalf("无效配置 %d 不应该返回调度器实例", i+1)
			}
		}

		t.Log("✅ 调度器配置验证通过")
	})

	t.Run("调度器启动和停止", func(t *testing.T) {
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "always", // 使用always模式便于测试
			Timezone: "Asia/Shanghai",
		}

		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		ctx := context.Background()

		// 启动调度器
		err = testScheduler.Start(ctx)
		if err != nil {
			t.Fatalf("启动调度器失败: %v", err)
		}

		// 检查状态
		status := testScheduler.GetStatus()
		if !status.IsRunning {
			t.Fatal("调度器状态显示未运行")
		}

		if status.Mode != "always" {
			t.Fatalf("调度模式不匹配: 期望 always, 实际 %s", status.Mode)
		}

		if status.Timezone != "Asia/Shanghai" {
			t.Fatalf("时区不匹配: 期望 Asia/Shanghai, 实际 %s", status.Timezone)
		}

		t.Log("✅ 调度器启动成功")

		// 停止调度器
		err = testScheduler.Stop()
		if err != nil {
			t.Fatalf("停止调度器失败: %v", err)
		}

		// 检查状态
		status = testScheduler.GetStatus()
		if status.IsRunning {
			t.Fatal("调度器状态显示仍在运行")
		}

		t.Log("✅ 调度器停止成功")
	})

	t.Run("下次调度时间计算", func(t *testing.T) {
		scheduleConfigs := []*config.ScheduleConfig{
			{Mode: "odd", Timezone: "Asia/Shanghai"},
			{Mode: "even", Timezone: "Asia/Shanghai"},
			{Mode: "always", Timezone: "Asia/Shanghai"},
		}

		for _, cfg := range scheduleConfigs {
			testScheduler, err := scheduler.NewScheduler(cfg, testCoordinator, nil, logger)
			if err != nil {
				t.Fatalf("创建调度器失败: %v", err)
			}

			nextTime := testScheduler.GetNextScheduledTime()
			if nextTime.IsZero() {
				t.Fatalf("下次调度时间不应为零值，模式: %s", cfg.Mode)
			}

			// 验证下次调度时间在未来
			if !nextTime.After(time.Now()) {
				t.Fatalf("下次调度时间应该在未来，模式: %s, 时间: %v", cfg.Mode, nextTime)
			}

			// 验证时间在合理范围内（不超过2小时）
			if nextTime.Sub(time.Now()) > 2*time.Hour {
				t.Fatalf("下次调度时间过远，模式: %s, 时间: %v", cfg.Mode, nextTime)
			}

			t.Logf("✅ 模式 %s 下次调度时间: %v", cfg.Mode, nextTime)
		}
	})

	t.Run("手动触发测试", func(t *testing.T) {
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "odd",
			Timezone: "Asia/Shanghai",
		}

		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		ctx := context.Background()

		// 启动调度器
		testScheduler.Start(ctx)

		// 手动触发测试
		err = testScheduler.TriggerTest(ctx)
		if err != nil {
			t.Fatalf("手动触发测试失败: %v", err)
		}

		// 等待一小段时间
		time.Sleep(100 * time.Millisecond)

		// 检查状态
		status := testScheduler.GetStatus()
		if status.TotalTests == 0 {
			t.Log("⚠️ 注意: 总测试数为0，可能是因为客户端连接失败（这在测试环境中是正常的）")
		}

		t.Log("✅ 手动触发测试完成")

		// 清理
		testScheduler.Stop()
	})

	t.Run("重复启动和停止", func(t *testing.T) {
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "even",
			Timezone: "Asia/Shanghai",
		}

		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		ctx := context.Background()

		// 启动调度器
		testScheduler.Start(ctx)

		// 重复启动
		err = testScheduler.Start(ctx)
		if err == nil {
			t.Fatal("重复启动调度器应该失败")
		}

		t.Log("✅ 重复启动调度器正确失败")

		// 停止调度器
		testScheduler.Stop()

		// 重复停止 - 应该成功（幂等操作）
		err = testScheduler.Stop()
		if err != nil {
			t.Fatalf("重复停止调度器失败: %v", err)
		}

		t.Log("✅ 重复停止调度器正确处理（幂等操作）")
	})

	t.Run("时区处理测试", func(t *testing.T) {
		timezones := []string{
			"UTC",
			"Asia/Shanghai",
			"America/New_York",
			"Europe/London",
			"Asia/Tokyo",
		}

		for _, tz := range timezones {
			scheduleConfig := &config.ScheduleConfig{
				Mode:     "always",
				Timezone: tz,
			}

			testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
			if err != nil {
				t.Fatalf("创建调度器失败，时区: %s, 错误: %v", tz, err)
			}

			nextTime := testScheduler.GetNextScheduledTime()
			if nextTime.IsZero() {
				t.Fatalf("下次调度时间为零值，时区: %s", tz)
			}

			status := testScheduler.GetStatus()
			if status.Timezone != tz {
				t.Fatalf("时区不匹配，期望: %s, 实际: %s", tz, status.Timezone)
			}

			t.Logf("✅ 时区 %s 处理正常", tz)
		}
	})

	t.Run("调度模式测试", func(t *testing.T) {
		modes := []string{"odd", "even", "always"}

		for _, mode := range modes {
			scheduleConfig := &config.ScheduleConfig{
				Mode:     mode,
				Timezone: "Asia/Shanghai",
			}

			testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
			if err != nil {
				t.Fatalf("创建调度器失败，模式: %s, 错误: %v", mode, err)
			}

			status := testScheduler.GetStatus()
			if status.Mode != mode {
				t.Fatalf("调度模式不匹配，期望: %s, 实际: %s", mode, status.Mode)
			}

			nextTime := testScheduler.GetNextScheduledTime()
			if nextTime.IsZero() {
				t.Fatalf("下次调度时间为零值，模式: %s", mode)
			}

			t.Logf("✅ 调度模式 %s 处理正常", mode)
		}
	})

	t.Run("上下文取消测试", func(t *testing.T) {
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "always",
			Timezone: "Asia/Shanghai",
		}

		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		ctx, cancel := context.WithCancel(context.Background())

		// 启动调度器
		testScheduler.Start(ctx)

		// 取消上下文
		cancel()

		// 等待一小段时间让取消生效
		time.Sleep(100 * time.Millisecond)

		// 尝试触发测试
		newCtx := context.Background()
		err = testScheduler.TriggerTest(newCtx)
		if err != nil {
			t.Log("✅ 上下文取消后操作正确处理")
		}

		// 清理
		testScheduler.Stop()
	})
}

// BenchmarkScheduler 调度器性能测试
func BenchmarkScheduler(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	clientConfig := &config.ClientManagementConfig{
		PrepareTimeout: 1 * time.Second,
		TestTimeout:    5 * time.Second,
		StopTimeout:    1 * time.Second,
		RetryAttempts:  1,
		RetryDelay:     100 * time.Millisecond,
	}

	clientManager := client.NewManager(clientConfig, logger)
	testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

	scheduleConfig := &config.ScheduleConfig{
		Mode:     "always",
		Timezone: "Asia/Shanghai",
	}

	testScheduler, _ := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)

	b.Run("GetStatus", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			testScheduler.GetStatus()
		}
	})

	b.Run("GetNextScheduledTime", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			testScheduler.GetNextScheduledTime()
		}
	})
}
