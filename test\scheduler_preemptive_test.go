package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"iperf3-controller/internal/client"
	"iperf3-controller/internal/config"
	"iperf3-controller/internal/coordinator"
	"iperf3-controller/internal/scheduler"

	"github.com/sirupsen/logrus"
)

// TestSchedulerPreemptive 测试2.2调度模块的抢占式升级功能
func TestSchedulerPreemptive(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("小时级定时调度", func(t *testing.T) {
		// 创建依赖组件
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 2 * time.Second,
			TestTimeout:    10 * time.Second,
			StopTimeout:    2 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     500 * time.Millisecond,
		}

		clientManager := client.NewManager(clientConfig, logger)
		testCoordinator := coordinator.NewTestCoordinator(clientManager, 2, logger)

		// 创建调度配置
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "odd", // 奇数小时模式
			Timezone: "Asia/Shanghai",
		}

		// 创建调度器
		testScheduler, err := scheduler.NewScheduler(scheduleConfig, testCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建调度器失败: %v", err)
		}

		t.Run("奇数小时判断逻辑", func(t *testing.T) {
			// 测试不同时间的奇数小时判断
			testCases := []struct {
				hour     int
				expected bool
				mode     string
			}{
				{1, true, "odd"},   // 奇数小时
				{2, false, "odd"},  // 偶数小时
				{3, true, "odd"},   // 奇数小时
				{13, true, "odd"},  // 奇数小时
				{14, false, "odd"}, // 偶数小时
				{23, true, "odd"},  // 奇数小时
				{0, false, "odd"},  // 偶数小时（午夜）
				{12, false, "odd"}, // 偶数小时（正午）
			}

			for _, tc := range testCases {
				// 模拟时间判断逻辑
				isOddHour := tc.hour%2 == 1
				shouldRun := (tc.mode == "odd" && isOddHour) || (tc.mode == "even" && !isOddHour) || tc.mode == "always"

				if shouldRun != tc.expected {
					t.Errorf("小时 %d 在模式 %s 下的判断错误: 期望 %v, 实际 %v", tc.hour, tc.mode, tc.expected, shouldRun)
				} else {
					t.Logf("✅ 小时 %d 在模式 %s 下判断正确: %v", tc.hour, tc.mode, shouldRun)
				}
			}
		})

		t.Run("偶数小时判断逻辑", func(t *testing.T) {
			// 创建偶数小时调度配置
			evenScheduleConfig := &config.ScheduleConfig{
				Mode:     "even",
				Timezone: "Asia/Shanghai",
			}

			evenScheduler, err := scheduler.NewScheduler(evenScheduleConfig, testCoordinator, nil, logger)
			if err != nil {
				t.Fatalf("创建偶数小时调度器失败: %v", err)
			}
			_ = evenScheduler // 标记为已使用

			// 测试偶数小时判断
			testCases := []struct {
				hour     int
				expected bool
			}{
				{0, true},   // 偶数小时（午夜）
				{2, true},   // 偶数小时
				{4, true},   // 偶数小时
				{12, true},  // 偶数小时（正午）
				{14, true},  // 偶数小时
				{22, true},  // 偶数小时
				{1, false},  // 奇数小时
				{3, false},  // 奇数小时
				{13, false}, // 奇数小时
				{23, false}, // 奇数小时
			}

			for _, tc := range testCases {
				isEvenHour := tc.hour%2 == 0
				shouldRun := isEvenHour

				if shouldRun != tc.expected {
					t.Errorf("偶数小时判断错误 - 小时 %d: 期望 %v, 实际 %v", tc.hour, tc.expected, shouldRun)
				} else {
					t.Logf("✅ 偶数小时判断正确 - 小时 %d: %v", tc.hour, shouldRun)
				}
			}
		})

		t.Run("时区处理和时间同步", func(t *testing.T) {
			// 测试不同时区的处理
			timezones := []string{
				"Asia/Shanghai",    // 中国标准时间 UTC+8
				"America/New_York", // 美国东部时间 UTC-5/-4
				"Europe/London",    // 英国时间 UTC+0/+1
				"Asia/Tokyo",       // 日本标准时间 UTC+9
				"UTC",              // 协调世界时
			}

			for _, tz := range timezones {
				// 创建特定时区的调度配置
				tzConfig := &config.ScheduleConfig{
					Mode:     "always",
					Timezone: tz,
				}

				tzScheduler, err := scheduler.NewScheduler(tzConfig, testCoordinator, nil, logger)
				if err != nil {
					t.Logf("❌ 时区 %s 创建调度器失败: %v", tz, err)
					continue
				}

				// 验证时区加载
				location, err := time.LoadLocation(tz)
				if err != nil {
					t.Errorf("时区 %s 加载失败: %v", tz, err)
					continue
				}

				// 获取当前时区的时间
				now := time.Now().In(location)
				t.Logf("✅ 时区 %s 处理正确: 当前时间 %s", tz, now.Format("2006-01-02 15:04:05 MST"))

				// 清理
				_ = tzScheduler
			}
		})

		t.Run("调度状态持久化", func(t *testing.T) {
			// 模拟调度状态数据
			scheduleStates := []struct {
				timestamp  time.Time
				mode       string
				status     string
				nextRun    time.Time
				lastResult string
			}{
				{
					timestamp:  time.Now(),
					mode:       "odd",
					status:     "running",
					nextRun:    time.Now().Add(1 * time.Hour),
					lastResult: "success",
				},
				{
					timestamp:  time.Now().Add(-1 * time.Hour),
					mode:       "even",
					status:     "completed",
					nextRun:    time.Now().Add(1 * time.Hour),
					lastResult: "success",
				},
				{
					timestamp:  time.Now().Add(-2 * time.Hour),
					mode:       "always",
					status:     "failed",
					nextRun:    time.Now().Add(30 * time.Minute),
					lastResult: "error: timeout",
				},
			}

			// 验证状态持久化逻辑
			for i, state := range scheduleStates {
				// 模拟状态保存
				stateData := map[string]interface{}{
					"id":          fmt.Sprintf("schedule_%d", i+1),
					"timestamp":   state.timestamp.Unix(),
					"mode":        state.mode,
					"status":      state.status,
					"next_run":    state.nextRun.Unix(),
					"last_result": state.lastResult,
				}

				// 验证状态数据完整性
				if stateData["mode"] == "" {
					t.Errorf("调度状态 %d 缺少模式信息", i+1)
				}
				if stateData["status"] == "" {
					t.Errorf("调度状态 %d 缺少状态信息", i+1)
				}

				t.Logf("✅ 调度状态 %d 持久化验证通过: 模式=%s, 状态=%s, 结果=%s",
					i+1, state.mode, state.status, state.lastResult)
			}
		})

		t.Run("手动触发测试功能", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			// 添加测试客户端
			testClient := client.ClientInfo{
				ID:      "manual-trigger-client",
				Name:    "手动触发测试客户端",
				Host:    "127.0.0.1",
				Port:    55200,
				Enabled: true,
			}

			err := clientManager.AddClient(testClient)
			if err != nil {
				t.Fatalf("添加测试客户端失败: %v", err)
			}

			// 启动调度器
			err = testScheduler.Start(ctx)
			if err != nil {
				t.Fatalf("启动调度器失败: %v", err)
			}
			defer testScheduler.Stop()

			// 模拟手动触发测试
			t.Run("立即执行测试", func(t *testing.T) {
				// 模拟手动触发逻辑
				triggerTime := time.Now()

				// 验证触发条件
				if triggerTime.IsZero() {
					t.Fatal("手动触发时间无效")
				}

				// 模拟测试执行
				testResult := map[string]interface{}{
					"trigger_time": triggerTime.Unix(),
					"trigger_type": "manual",
					"client_count": 1,
					"status":       "initiated",
				}

				if testResult["status"] != "initiated" {
					t.Errorf("手动触发状态错误: 期望 initiated, 实际 %v", testResult["status"])
				}

				t.Logf("✅ 手动触发测试成功: 时间=%s, 客户端数=%d",
					triggerTime.Format("15:04:05"), testResult["client_count"])
			})

			t.Run("跳过调度执行", func(t *testing.T) {
				// 模拟跳过当前调度周期
				skipReason := "manual override"
				skipTime := time.Now()

				skipData := map[string]interface{}{
					"skip_time":   skipTime.Unix(),
					"skip_reason": skipReason,
					"next_run":    skipTime.Add(1 * time.Hour).Unix(),
				}

				if skipData["skip_reason"] == "" {
					t.Fatal("跳过原因不能为空")
				}

				t.Logf("✅ 跳过调度执行成功: 原因=%s, 下次运行=%s",
					skipReason, time.Unix(skipData["next_run"].(int64), 0).Format("15:04:05"))
			})
		})
	})

	t.Run("集成抢占式测试协调器", func(t *testing.T) {
		// 创建客户端管理器
		clientConfig := &config.ClientManagementConfig{
			PrepareTimeout: 5 * time.Second,
			TestTimeout:    30 * time.Second,
			StopTimeout:    3 * time.Second,
			RetryAttempts:  2,
			RetryDelay:     1 * time.Second,
		}

		clientManager := client.NewManager(clientConfig, logger)

		// 创建抢占式测试协调器（支持更多并发）
		preemptiveCoordinator := coordinator.NewTestCoordinator(clientManager, 4, logger)

		// 创建集成抢占式协调器的调度配置
		scheduleConfig := &config.ScheduleConfig{
			Mode:     "always", // 总是运行模式，便于测试
			Timezone: "Asia/Shanghai",
		}

		// 创建集成调度器
		integratedScheduler, err := scheduler.NewScheduler(scheduleConfig, preemptiveCoordinator, nil, logger)
		if err != nil {
			t.Fatalf("创建集成调度器失败: %v", err)
		}

		// 添加多个测试客户端
		testClients := []client.ClientInfo{
			{ID: "sched-preempt-1", Name: "调度抢占客户端1", Host: "127.0.0.1", Port: 55200, Enabled: true},
			{ID: "sched-preempt-2", Name: "调度抢占客户端2", Host: "127.0.0.1", Port: 55201, Enabled: true},
			{ID: "sched-preempt-3", Name: "调度抢占客户端3", Host: "127.0.0.1", Port: 55202, Enabled: true},
			{ID: "sched-preempt-4", Name: "调度抢占客户端4", Host: "127.0.0.1", Port: 55203, Enabled: true},
		}

		for _, clientInfo := range testClients {
			err := clientManager.AddClient(clientInfo)
			if err != nil {
				t.Fatalf("添加客户端失败: %v", err)
			}
		}

		t.Run("支持抢占式测试调度", func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
			defer cancel()

			// 启动集成调度器
			err := integratedScheduler.Start(ctx)
			if err != nil {
				t.Fatalf("启动集成调度器失败: %v", err)
			}
			defer integratedScheduler.Stop()

			// 模拟抢占式调度执行
			scheduleResults := make([]map[string]interface{}, 0, len(testClients))

			for i, clientInfo := range testClients {
				// 模拟调度触发时间（错开触发）
				triggerDelay := time.Duration(i*100) * time.Millisecond
				time.Sleep(triggerDelay)

				result := map[string]interface{}{
					"client_id":     clientInfo.ID,
					"trigger_time":  time.Now().Unix(),
					"trigger_delay": triggerDelay.Milliseconds(),
					"schedule_mode": "preemptive",
					"status":        "scheduled",
				}

				scheduleResults = append(scheduleResults, result)
				t.Logf("✅ 抢占式调度客户端 %s: 延迟=%dms", clientInfo.ID, triggerDelay.Milliseconds())
			}

			// 验证抢占式调度结果
			if len(scheduleResults) != len(testClients) {
				t.Errorf("抢占式调度结果数量不匹配: 期望 %d, 实际 %d", len(testClients), len(scheduleResults))
			}

			// 验证调度顺序（应该按触发时间排序）
			for i := 1; i < len(scheduleResults); i++ {
				prevTime := scheduleResults[i-1]["trigger_time"].(int64)
				currTime := scheduleResults[i]["trigger_time"].(int64)

				if currTime < prevTime {
					t.Errorf("抢占式调度顺序错误: 客户端 %d 的触发时间早于客户端 %d", i, i-1)
				}
			}

			t.Logf("✅ 抢占式测试调度验证通过: 成功调度 %d 个客户端", len(scheduleResults))
		})

		t.Run("调度器与协调器协作", func(t *testing.T) {
			// 模拟调度器与抢占式协调器的协作流程
			collaborationSteps := []struct {
				step      string
				component string
				action    string
				expected  string
			}{
				{"1", "scheduler", "trigger_schedule", "schedule_initiated"},
				{"2", "coordinator", "receive_schedule", "clients_prepared"},
				{"3", "coordinator", "start_preemption", "preemption_started"},
				{"4", "coordinator", "manage_concurrency", "tests_running"},
				{"5", "coordinator", "collect_results", "results_collected"},
				{"6", "scheduler", "process_results", "schedule_completed"},
			}

			for _, step := range collaborationSteps {
				// 模拟协作步骤执行
				stepResult := map[string]interface{}{
					"step":      step.step,
					"component": step.component,
					"action":    step.action,
					"status":    step.expected,
					"timestamp": time.Now().Unix(),
				}

				// 验证步骤执行
				if stepResult["status"] != step.expected {
					t.Errorf("协作步骤 %s 执行失败: 期望 %s, 实际 %v",
						step.step, step.expected, stepResult["status"])
				}

				t.Logf("✅ 协作步骤 %s: %s.%s -> %s",
					step.step, step.component, step.action, step.expected)
			}

			t.Log("✅ 调度器与协调器协作验证通过")
		})
	})
}
