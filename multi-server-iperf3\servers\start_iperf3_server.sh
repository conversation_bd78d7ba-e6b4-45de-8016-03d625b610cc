#!/bin/bash

# iperf3服务器端启动脚本
# 用于在12台服务器上启动iperf3服务

# 配置
IPERF3_PORT=5201
PID_FILE="/var/run/iperf3.pid"
LOG_FILE="/var/log/iperf3.log"
USER="iperf3"  # 运行用户（可选）

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

# 检查iperf3是否已安装
check_iperf3() {
    if ! command -v iperf3 >/dev/null 2>&1; then
        log_error "iperf3未安装，请先安装iperf3"
        echo "Ubuntu/Debian: sudo apt-get install iperf3"
        echo "CentOS/RHEL: sudo yum install iperf3"
        echo "或者: sudo dnf install iperf3"
        exit 1
    fi
    
    log_success "iperf3已安装: $(iperf3 --version | head -1)"
}

# 检查端口是否被占用
check_port() {
    if netstat -tuln 2>/dev/null | grep -q ":$IPERF3_PORT "; then
        log_warning "端口 $IPERF3_PORT 已被占用"
        
        # 检查是否是iperf3进程
        local pid=$(lsof -ti:$IPERF3_PORT 2>/dev/null)
        if [ -n "$pid" ]; then
            local process=$(ps -p $pid -o comm= 2>/dev/null)
            if [ "$process" = "iperf3" ]; then
                log_warning "端口被iperf3进程占用 (PID: $pid)"
                return 1
            else
                log_error "端口被其他进程占用: $process (PID: $pid)"
                exit 1
            fi
        fi
    fi
    return 0
}

# 启动iperf3服务器
start_iperf3() {
    log "启动iperf3服务器..."
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            log_warning "iperf3服务器已在运行 (PID: $old_pid)"
            return 0
        else
            log "清理旧的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 启动iperf3服务器
    local cmd="iperf3 -s -p $IPERF3_PORT -D --pidfile $PID_FILE"
    
    # 如果指定了用户，使用sudo切换用户
    if [ -n "$USER" ] && [ "$USER" != "$(whoami)" ]; then
        if id "$USER" >/dev/null 2>&1; then
            cmd="sudo -u $USER $cmd"
        else
            log_warning "用户 $USER 不存在，使用当前用户运行"
        fi
    fi
    
    # 执行启动命令
    if eval "$cmd" >> "$LOG_FILE" 2>&1; then
        sleep 2  # 等待进程完全启动
        
        if [ -f "$PID_FILE" ]; then
            local pid=$(cat "$PID_FILE")
            if kill -0 "$pid" 2>/dev/null; then
                log_success "iperf3服务器启动成功"
                log "PID: $pid"
                log "端口: $IPERF3_PORT"
                log "日志: $LOG_FILE"
                log "PID文件: $PID_FILE"
                return 0
            fi
        fi
    fi
    
    log_error "iperf3服务器启动失败"
    if [ -f "$LOG_FILE" ]; then
        log "错误日志:"
        tail -10 "$LOG_FILE"
    fi
    return 1
}

# 停止iperf3服务器
stop_iperf3() {
    log "停止iperf3服务器..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            if kill "$pid" 2>/dev/null; then
                # 等待进程结束
                local count=0
                while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                    sleep 1
                    ((count++))
                done
                
                if kill -0 "$pid" 2>/dev/null; then
                    log_warning "进程未正常结束，强制终止"
                    kill -9 "$pid" 2>/dev/null
                fi
                
                log_success "iperf3服务器已停止"
            else
                log_error "无法停止iperf3进程 (PID: $pid)"
                return 1
            fi
        else
            log_warning "PID文件存在但进程不存在"
        fi
        rm -f "$PID_FILE"
    else
        # 尝试通过端口查找进程
        local pid=$(lsof -ti:$IPERF3_PORT 2>/dev/null)
        if [ -n "$pid" ]; then
            local process=$(ps -p $pid -o comm= 2>/dev/null)
            if [ "$process" = "iperf3" ]; then
                log "发现运行中的iperf3进程 (PID: $pid)，正在停止..."
                if kill "$pid" 2>/dev/null; then
                    log_success "iperf3服务器已停止"
                else
                    log_error "无法停止iperf3进程"
                    return 1
                fi
            fi
        else
            log_warning "未发现运行中的iperf3服务器"
        fi
    fi
    
    return 0
}

# 重启iperf3服务器
restart_iperf3() {
    log "重启iperf3服务器..."
    stop_iperf3
    sleep 2
    start_iperf3
}

# 查看iperf3状态
status_iperf3() {
    log "检查iperf3服务器状态..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_success "iperf3服务器正在运行"
            log "PID: $pid"
            log "端口: $IPERF3_PORT"
            log "运行时间: $(ps -o etime= -p $pid 2>/dev/null | tr -d ' ')"
            
            # 显示网络连接
            if command -v netstat >/dev/null 2>&1; then
                log "网络连接:"
                netstat -tuln | grep ":$IPERF3_PORT "
            fi
            
            return 0
        else
            log_error "PID文件存在但进程不存在"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查端口是否被占用
    local pid=$(lsof -ti:$IPERF3_PORT 2>/dev/null)
    if [ -n "$pid" ]; then
        local process=$(ps -p $pid -o comm= 2>/dev/null)
        if [ "$process" = "iperf3" ]; then
            log_warning "发现运行中的iperf3进程但无PID文件 (PID: $pid)"
            echo "$pid" > "$PID_FILE"
            return 0
        else
            log_error "端口被其他进程占用: $process (PID: $pid)"
            return 1
        fi
    fi
    
    log_warning "iperf3服务器未运行"
    return 1
}

# 测试iperf3服务器
test_iperf3() {
    log "测试iperf3服务器..."
    
    if ! status_iperf3 >/dev/null 2>&1; then
        log_error "iperf3服务器未运行"
        return 1
    fi
    
    # 执行简单的连接测试
    if timeout 10 iperf3 -c localhost -p $IPERF3_PORT -t 1 >/dev/null 2>&1; then
        log_success "iperf3服务器测试通过"
        return 0
    else
        log_error "iperf3服务器测试失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "iperf3服务器管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动iperf3服务器"
    echo "  stop      停止iperf3服务器"
    echo "  restart   重启iperf3服务器"
    echo "  status    查看iperf3服务器状态"
    echo "  test      测试iperf3服务器"
    echo "  help      显示此帮助信息"
    echo ""
    echo "配置:"
    echo "  端口: $IPERF3_PORT"
    echo "  PID文件: $PID_FILE"
    echo "  日志文件: $LOG_FILE"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务器"
    echo "  $0 status   # 查看状态"
    echo "  $0 test     # 测试连接"
}

# 主函数
main() {
    # 检查iperf3安装
    check_iperf3
    
    local command=${1:-help}
    
    case $command in
        "start")
            if ! check_port; then
                log_warning "端口检查发现问题，继续启动..."
            fi
            start_iperf3
            ;;
        "stop")
            stop_iperf3
            ;;
        "restart")
            restart_iperf3
            ;;
        "status")
            status_iperf3
            ;;
        "test")
            test_iperf3
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
