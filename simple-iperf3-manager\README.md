# 🚀 iperf3 简化管理系统

一个轻量级的iperf3测试管理解决方案，专为OpenWRT到服务器的网络测试设计。

## 🎯 功能特点

- ✅ **自动管理服务器iperf3**: 需要时启动，不需要时关闭
- ✅ **OpenWRT端发起测试**: 简单的bash脚本，易于部署
- ✅ **SQLite数据存储**: 本地存储测试结果，方便查看
- ✅ **TCP/UDP双重测试**: 全面评估网络质量
- ✅ **丰富的查看工具**: 多种方式查看和分析结果
- ✅ **跨平台支持**: 支持Windows开发、Linux部署

## 📁 项目结构

```
simple-iperf3-manager/
├── server/
│   └── main.go              # 服务器端iperf3管理器
├── openwrt/
│   ├── test_script.sh       # OpenWRT测试脚本
│   └── view_results.sh      # 结果查看工具
└── README.md               # 说明文档
```

## 🚀 快速开始

### 1. 服务器端部署

```bash
# 编译服务器端程序
cd server
go build -o iperf3-manager main.go

# 启动管理器
./iperf3-manager
```

服务器将在端口8080提供API服务：
- `GET /status` - 查看iperf3状态
- `POST /start` - 启动iperf3服务
- `POST /stop` - 停止iperf3服务

### 2. OpenWRT端部署

```bash
# 复制脚本到OpenWRT
scp openwrt/*.sh root@openwrt-ip:/root/

# 登录OpenWRT
ssh root@openwrt-ip

# 设置执行权限
chmod +x test_script.sh view_results.sh

# 修改配置（编辑脚本中的SERVER_IP）
vi test_script.sh
```

### 3. 运行测试

```bash
# 执行测试
./test_script.sh

# 查看结果
./view_results.sh recent 10    # 最近10次结果
./view_results.sh stats        # 统计信息
./view_results.sh today        # 今日结果
```

## 📊 使用示例

### 执行测试
```bash
root@openwrt:~# ./test_script.sh
🚀 iperf3 简化测试脚本启动
================================
[INFO] 2025-07-31 10:30:15 - 初始化SQLite数据库...
[SUCCESS] 2025-07-31 10:30:15 - 数据库初始化完成
[INFO] 2025-07-31 10:30:15 - 检查服务器API连接...
[SUCCESS] 2025-07-31 10:30:15 - 服务器API连接正常
[INFO] 2025-07-31 10:30:15 - 启动服务器iperf3服务...
[SUCCESS] 2025-07-31 10:30:16 - 服务器iperf3启动成功
[INFO] 2025-07-31 10:30:18 - 开始TCP速度测试...
[SUCCESS] 2025-07-31 10:30:48 - TCP测试完成
[SUCCESS] 2025-07-31 10:30:48 - TCP测试结果已保存 - 带宽: 850.25 Mbps, 重传: 0
[INFO] 2025-07-31 10:30:50 - 开始UDP质量测试...
[SUCCESS] 2025-07-31 10:31:20 - UDP测试完成
[SUCCESS] 2025-07-31 10:31:20 - UDP测试结果已保存 - 带宽: 95.80 Mbps, 丢包率: 0.05%, 抖动: 0.12 ms
[INFO] 2025-07-31 10:31:20 - 停止服务器iperf3服务...
[SUCCESS] 2025-07-31 10:31:21 - 服务器iperf3停止成功

📊 最近5次测试结果：
测试时间              类型  带宽        重传/丢包    状态     
2025-07-31 10:31:20  UDP   95.80 Mbps  0.05%       completed
2025-07-31 10:30:48  TCP   850.25 Mbps 0 次        completed

================================
[SUCCESS] 2025-07-31 10:31:21 - 所有测试完成！
```

### 查看统计信息
```bash
root@openwrt:~# ./view_results.sh stats
📈 测试统计信息
================================
总体统计:
总测试次数  成功次数  失败次数  成功率
20         19       1        95.0%

TCP测试统计:
TCP测试次数  平均带宽      最高带宽      最低带宽      平均重传次数
10          845.30 Mbps  920.15 Mbps  780.45 Mbps  0.2

UDP测试统计:
UDP测试次数  平均带宽     最高带宽     最低带宽     平均丢包率  平均抖动
10          94.85 Mbps  98.20 Mbps  89.30 Mbps  0.08%      0.15 ms
```

## 🔧 配置说明

### 服务器端配置
- **端口**: 默认8080（API端口）
- **iperf3端口**: 默认5201
- **CORS**: 已启用，支持跨域访问

### OpenWRT端配置
编辑 `test_script.sh` 中的配置：
```bash
SERVER_IP="*************"        # 服务器IP地址
SERVER_API_PORT="8080"           # 服务器API端口
IPERF3_PORT="5201"              # iperf3测试端口
DB_FILE="./iperf3_results.db"   # SQLite数据库文件路径
```

## 📋 数据库结构

SQLite数据库包含以下字段：
- **基本信息**: id, timestamp, test_type, server_ip, duration
- **TCP结果**: tcp_bandwidth_bps, tcp_bandwidth_mbps, tcp_retransmits, tcp_cwnd
- **UDP结果**: udp_bandwidth_bps, udp_bandwidth_mbps, udp_jitter_ms, udp_lost_packets, udp_total_packets, udp_lost_percent
- **原始数据**: raw_json (完整的iperf3 JSON输出)
- **状态信息**: status, error_message

## 🛠️ 高级功能

### 定时测试
使用cron定时执行测试：
```bash
# 每小时执行一次测试
0 * * * * /root/test_script.sh >> /var/log/iperf3_test.log 2>&1
```

### 数据导出
```bash
# 导出为CSV格式
./view_results.sh export csv

# 导出为JSON格式
./view_results.sh export json
```

### 数据清理
```bash
# 清理30天前的数据
./view_results.sh cleanup 30

# 清理7天前的数据
./view_results.sh cleanup 7
```

## 🔍 故障排除

### 常见问题

1. **无法连接到服务器API**
   - 检查服务器IP和端口配置
   - 确认服务器端程序正在运行
   - 检查防火墙设置

2. **iperf3测试失败**
   - 确认服务器已安装iperf3
   - 检查网络连通性
   - 查看错误日志

3. **数据库操作失败**
   - 确认SQLite已安装
   - 检查文件权限
   - 确认磁盘空间充足

### 日志查看
```bash
# 查看最近的错误
./view_results.sh recent 10 | grep failed

# 查看详细错误信息
./view_results.sh detail <test_id>
```

## 🆚 与完整系统的对比

| 功能 | 简化版本 | 完整系统 |
|------|----------|----------|
| 代码量 | ~300行 | ~5000行 |
| 部署复杂度 | 简单 | 复杂 |
| 服务器管理 | ✅ | ✅ |
| 数据存储 | SQLite | SQLite + Web |
| Web界面 | ❌ | ✅ |
| 多设备管理 | ❌ | ✅ |
| 抢占式测试 | ❌ | ✅ |
| 定时调度 | 手动cron | 内置调度器 |

## 📈 扩展建议

如果后续需要更多功能，可以考虑：
1. **添加Web界面**: 基于现有数据库开发简单的Web查看界面
2. **多设备支持**: 扩展脚本支持多个OpenWRT设备
3. **告警功能**: 当测试结果异常时发送通知
4. **图表展示**: 添加性能趋势图表
5. **配置文件**: 使用配置文件替代硬编码配置

## 📄 许可证

MIT License - 自由使用和修改

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
