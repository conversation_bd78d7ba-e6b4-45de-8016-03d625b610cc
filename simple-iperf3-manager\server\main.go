package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"syscall"
	"time"
)

type IperfManager struct {
	process *os.Process
	port    int
	running bool
}

type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Port    int    `json:"port,omitempty"`
	PID     int    `json:"pid,omitempty"`
}

var manager = &IperfManager{port: 5201}

func main() {
	http.HandleFunc("/start", startHandler)
	http.HandleFunc("/stop", stopHandler)
	http.HandleFunc("/status", statusHandler)
	
	// 启用CORS
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.<PERSON>er().Set("Access-Control-Allow-Headers", "Content-Type")
		
		if r.Method == "OPTIONS" {
			return
		}
		
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(Response{
			Success: false,
			Message: "API endpoints: /start, /stop, /status",
		})
	})
	
	log.Println("🚀 iperf3 管理器启动在端口 8080")
	log.Println("API endpoints:")
	log.Println("  GET  /status - 查看iperf3状态")
	log.Println("  POST /start  - 启动iperf3服务")
	log.Println("  POST /stop   - 停止iperf3服务")
	
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func startHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	if manager.running {
		json.NewEncoder(w).Encode(Response{
			Success: false,
			Message: "iperf3 已经在运行",
			Port:    manager.port,
			PID:     manager.process.Pid,
		})
		return
	}
	
	// 启动iperf3服务器
	cmd := exec.Command("iperf3", "-s", "-p", strconv.Itoa(manager.port), "-D")
	err := cmd.Start()
	if err != nil {
		json.NewEncoder(w).Encode(Response{
			Success: false,
			Message: fmt.Sprintf("启动iperf3失败: %v", err),
		})
		return
	}
	
	manager.process = cmd.Process
	manager.running = true
	
	log.Printf("✅ iperf3服务器已启动 - PID: %d, Port: %d", manager.process.Pid, manager.port)
	
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: "iperf3服务器启动成功",
		Port:    manager.port,
		PID:     manager.process.Pid,
	})
}

func stopHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	if !manager.running || manager.process == nil {
		json.NewEncoder(w).Encode(Response{
			Success: false,
			Message: "iperf3 没有在运行",
		})
		return
	}
	
	// 停止iperf3进程
	err := manager.process.Signal(syscall.SIGTERM)
	if err != nil {
		// 如果SIGTERM失败，尝试SIGKILL
		manager.process.Kill()
	}
	
	// 等待进程结束
	go func() {
		manager.process.Wait()
		manager.running = false
		manager.process = nil
	}()
	
	log.Printf("🛑 iperf3服务器已停止 - PID: %d", manager.process.Pid)
	
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: "iperf3服务器停止成功",
	})
}

func statusHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	if manager.running && manager.process != nil {
		// 检查进程是否还活着
		err := manager.process.Signal(syscall.Signal(0))
		if err != nil {
			manager.running = false
			manager.process = nil
		}
	}
	
	response := Response{
		Success: true,
		Message: "iperf3状态查询成功",
	}
	
	if manager.running {
		response.Message = "iperf3 正在运行"
		response.Port = manager.port
		response.PID = manager.process.Pid
	} else {
		response.Message = "iperf3 未运行"
	}
	
	json.NewEncoder(w).Encode(response)
}
