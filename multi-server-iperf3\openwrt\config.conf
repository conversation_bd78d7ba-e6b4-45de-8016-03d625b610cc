# iperf3多服务器测试配置文件
# OpenWRT端配置

# 数据库配置
DB_FILE="./iperf3_results.db"

# Web服务配置
WEB_PORT=8080
WEB_DIR="./web"

# 测试配置
TEST_DURATION=30
TCP_PARALLEL_STREAMS=4
UDP_BANDWIDTH="100M"
TEST_TIMEOUT=60

# 并发配置
MAX_CONCURRENT_TESTS=3
TEST_INTERVAL=2

# 服务器列表配置
# 格式: 服务器名称|IP地址|端口|描述|是否启用
SERVERS=(
    "Server-01|*************|5201|主服务器|true"
    "Server-02|*************|5201|备用服务器|true"
    "Server-03|*************|5201|数据库服务器|true"
    "Server-04|*************|5201|Web服务器|true"
    "Server-05|*************|5201|API服务器|true"
    "Server-06|*************|5201|缓存服务器|true"
    "Server-07|*************|5201|存储服务器|true"
    "Server-08|*************|5201|计算服务器|true"
    "Server-09|*************|5201|监控服务器|true"
    "Server-10|*************|5201|日志服务器|true"
    "Server-11|*************|5201|测试服务器|true"
    "Server-12|*************|5201|开发服务器|true"
)

# 日志配置
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR
LOG_FILE="./iperf3_test.log"
LOG_MAX_SIZE="10M"

# 告警配置
ALERT_ENABLED=true
ALERT_TCP_MIN_SPEED=100  # Mbps
ALERT_UDP_MAX_LOSS=5     # %
ALERT_MAX_LATENCY=100    # ms

# 数据保留配置
DATA_RETENTION_DAYS=90
AUTO_CLEANUP=true
