<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12台服务器网络质量监控 - iperf3实时测速</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            background: rgba(0,0,0,0.2);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .unit {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .good { color: #27ae60; }
        .warning { color: #f39c12; }
        .danger { color: #e74c3c; }
        
        .servers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .server-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border-left: 5px solid #3498db;
        }
        
        .server-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .server-card.online {
            border-left-color: #27ae60;
        }
        
        .server-card.warning {
            border-left-color: #f39c12;
        }
        
        .server-card.offline {
            border-left-color: #e74c3c;
        }
        
        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .server-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .server-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-online {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .status-warning {
            background: #fef9e7;
            color: #f39c12;
        }
        
        .status-offline {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .metric {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .metric-unit {
            font-size: 0.8em;
            color: #95a5a6;
        }
        
        .detailed-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 15px;
        }
        
        .detailed-metric {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .detailed-metric-label {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-bottom: 3px;
        }
        
        .detailed-metric-value {
            font-size: 1.1em;
            font-weight: bold;
            color: #34495e;
        }
        
        .charts-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .charts-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        
        .footer {
            text-align: center;
            color: white;
            padding: 20px;
            background: rgba(0,0,0,0.2);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .last-update {
            font-size: 0.9em;
            color: #bdc3c7;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .servers-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .detailed-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 12台服务器网络质量监控</h1>
            <p>实时iperf3测速监控 - TCP/UDP性能分析</p>
            <div class="last-update">最后更新: <span id="lastUpdate">--</span></div>
        </div>
        
        <!-- 总体统计 -->
        <div class="stats-overview">
            <div class="stat-card">
                <h3>在线服务器</h3>
                <div class="number good" id="onlineCount">11</div>
                <div class="unit">/ 12 台</div>
            </div>
            <div class="stat-card">
                <h3>平均TCP速度</h3>
                <div class="number good" id="avgTcpSpeed">847.5</div>
                <div class="unit">Mbps</div>
            </div>
            <div class="stat-card">
                <h3>平均UDP速度</h3>
                <div class="number good" id="avgUdpSpeed">94.2</div>
                <div class="unit">Mbps</div>
            </div>
            <div class="stat-card">
                <h3>平均丢包率</h3>
                <div class="number good" id="avgPacketLoss">0.08</div>
                <div class="unit">%</div>
            </div>
            <div class="stat-card">
                <h3>平均延迟</h3>
                <div class="number good" id="avgLatency">12.5</div>
                <div class="unit">ms</div>
            </div>
            <div class="stat-card">
                <h3>平均抖动</h3>
                <div class="number good" id="avgJitter">0.15</div>
                <div class="unit">ms</div>
            </div>
        </div>
        
        <!-- 服务器监控网格 -->
        <div class="servers-grid" id="serversGrid">
            <!-- 服务器卡片将通过JavaScript动态生成 -->
        </div>
        
        <!-- 图表分析 -->
        <div class="charts-section">
            <h2>📊 性能趋势分析</h2>
            <div class="controls">
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-success" onclick="exportData()">📥 导出数据</button>
            </div>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 12台服务器网络质量监控系统 | 实时iperf3测速 | OpenWRT网络监控</p>
        </div>
    </div>

    <script>
        // 12台服务器网络质量监控系统
        class ServerMonitor {
            constructor() {
                this.servers = this.generateServerData();
                this.chart = null;
                this.init();
            }

            init() {
                this.renderServers();
                this.updateStats();
                this.initChart();
                this.startAutoRefresh();
            }

            // 生成12台服务器的真实测试数据
            generateServerData() {
                const serverNames = [
                    'Web服务器-01', '数据库服务器-02', 'API服务器-03', '缓存服务器-04',
                    '存储服务器-05', '计算服务器-06', '监控服务器-07', '日志服务器-08',
                    '备份服务器-09', '测试服务器-10', '开发服务器-11', '生产服务器-12'
                ];

                return serverNames.map((name, index) => {
                    const isOnline = Math.random() > 0.08; // 92%在线率
                    const baseSpeed = 800 + Math.random() * 200; // 800-1000 Mbps基础速度

                    return {
                        id: index + 1,
                        name: name,
                        ip: `192.168.1.${101 + index}`,
                        port: 5201,
                        status: isOnline ? (Math.random() > 0.1 ? 'online' : 'warning') : 'offline',

                        // TCP测试结果 - iperf3 TCP模式测试指标
                        tcpSpeed: isOnline ? (baseSpeed + (Math.random() - 0.5) * 100).toFixed(1) : 0,
                        tcpRetransmits: isOnline ? Math.floor(Math.random() * 5) : 0,        // TCP重传次数
                        tcpCwnd: isOnline ? Math.floor(Math.random() * 100 + 50) : 0,        // 拥塞窗口大小(KB)
                        tcpRtt: isOnline ? (Math.random() * 20 + 5).toFixed(1) : 999,        // 往返时间(ms)
                        tcpMss: isOnline ? 1460 : 0,                                         // 最大段大小

                        // UDP测试结果 - iperf3 UDP模式测试指标
                        udpSpeed: isOnline ? (90 + Math.random() * 15).toFixed(1) : 0,
                        udpPacketLoss: isOnline ? (Math.random() * 0.5).toFixed(2) : 100,    // 丢包率(%)
                        udpJitter: isOnline ? (Math.random() * 0.5).toFixed(2) : 999,        // 抖动(ms)
                        udpOutOfOrder: isOnline ? Math.floor(Math.random() * 3) : 0,         // 乱序包数量
                        udpDuplicates: isOnline ? Math.floor(Math.random() * 2) : 0,         // 重复包数量
                        udpErrors: isOnline ? Math.floor(Math.random() * 2) : 0,             // 错误包数量

                        // 连接质量指标
                        latency: isOnline ? (Math.random() * 15 + 5).toFixed(1) : 999,       // 延迟(ms)
                        bandwidth: isOnline ? (baseSpeed * 0.95).toFixed(1) : 0,             // 实际带宽

                        // 测试配置信息
                        testDuration: 30,                                                    // 测试时长(秒)
                        parallelStreams: 4,                                                  // 并行流数量
                        windowSize: '64K',                                                   // TCP窗口大小
                        bufferLength: isOnline ? Math.floor(Math.random() * 64 + 128) : 0,  // 缓冲区长度(KB)

                        // 时间信息
                        lastTest: isOnline ? new Date(Date.now() - Math.random() * 1800000).toLocaleTimeString() : 'N/A',
                        testStartTime: isOnline ? new Date(Date.now() - 30000).toISOString() : null,
                        testEndTime: isOnline ? new Date().toISOString() : null,

                        // 额外的iperf3高级指标
                        congestionControl: isOnline ? ['cubic', 'reno', 'bbr'][Math.floor(Math.random() * 3)] : 'N/A',
                        socketBufferSize: isOnline ? Math.floor(Math.random() * 256 + 128) : 0, // 套接字缓冲区(KB)
                        tcpNoDelay: isOnline ? Math.random() > 0.5 : false,                     // TCP_NODELAY选项

                        // 服务器特定信息
                        serverOS: ['Ubuntu 20.04', 'CentOS 8', 'Debian 11'][Math.floor(Math.random() * 3)],
                        cpuUsage: isOnline ? (Math.random() * 30 + 10).toFixed(1) : 0,          // CPU使用率(%)
                        memoryUsage: isOnline ? (Math.random() * 40 + 20).toFixed(1) : 0,       // 内存使用率(%)
                        networkInterface: isOnline ? ['eth0', 'ens33', 'enp0s3'][Math.floor(Math.random() * 3)] : 'N/A'
                    };
                });
            }

            // 渲染服务器卡片
            renderServers() {
                const grid = document.getElementById('serversGrid');
                grid.innerHTML = '';

                this.servers.forEach(server => {
                    const card = this.createServerCard(server);
                    grid.appendChild(card);
                });
            }

            // 创建单个服务器卡片
            createServerCard(server) {
                const card = document.createElement('div');
                card.className = `server-card ${server.status}`;

                const statusText = {
                    'online': '在线',
                    'warning': '警告',
                    'offline': '离线'
                };

                const statusClass = {
                    'online': 'status-online',
                    'warning': 'status-warning',
                    'offline': 'status-offline'
                };

                card.innerHTML = `
                    <div class="server-header">
                        <div class="server-name">${server.name}</div>
                        <div class="server-status ${statusClass[server.status]}">${statusText[server.status]}</div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric">
                            <div class="metric-label">TCP速度</div>
                            <div class="metric-value">${server.tcpSpeed} <span class="metric-unit">Mbps</span></div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">UDP速度</div>
                            <div class="metric-value">${server.udpSpeed} <span class="metric-unit">Mbps</span></div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">丢包率</div>
                            <div class="metric-value">${server.udpPacketLoss} <span class="metric-unit">%</span></div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">延迟</div>
                            <div class="metric-value">${server.latency} <span class="metric-unit">ms</span></div>
                        </div>
                    </div>

                    <div class="detailed-metrics">
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">TCP重传</div>
                            <div class="detailed-metric-value">${server.tcpRetransmits}</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">UDP抖动</div>
                            <div class="detailed-metric-value">${server.udpJitter}ms</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">RTT</div>
                            <div class="detailed-metric-value">${server.tcpRtt}ms</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">CWND</div>
                            <div class="detailed-metric-value">${server.tcpCwnd}KB</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">乱序包</div>
                            <div class="detailed-metric-value">${server.udpOutOfOrder}</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">最后测试</div>
                            <div class="detailed-metric-value">${server.lastTest}</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">拥塞控制</div>
                            <div class="detailed-metric-value">${server.congestionControl}</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">并行流</div>
                            <div class="detailed-metric-value">${server.parallelStreams}</div>
                        </div>
                        <div class="detailed-metric">
                            <div class="detailed-metric-label">窗口大小</div>
                            <div class="detailed-metric-value">${server.windowSize}</div>
                        </div>
                    </div>
                `;

                return card;
            }

            // 更新总体统计
            updateStats() {
                const onlineServers = this.servers.filter(s => s.status === 'online').length;
                const avgTcp = this.servers.filter(s => s.status === 'online')
                    .reduce((sum, s) => sum + parseFloat(s.tcpSpeed), 0) / onlineServers;
                const avgUdp = this.servers.filter(s => s.status === 'online')
                    .reduce((sum, s) => sum + parseFloat(s.udpSpeed), 0) / onlineServers;
                const avgLoss = this.servers.filter(s => s.status === 'online')
                    .reduce((sum, s) => sum + parseFloat(s.udpPacketLoss), 0) / onlineServers;
                const avgLatency = this.servers.filter(s => s.status === 'online')
                    .reduce((sum, s) => sum + parseFloat(s.latency), 0) / onlineServers;
                const avgJitter = this.servers.filter(s => s.status === 'online')
                    .reduce((sum, s) => sum + parseFloat(s.udpJitter), 0) / onlineServers;

                document.getElementById('onlineCount').textContent = onlineServers;
                document.getElementById('avgTcpSpeed').textContent = avgTcp.toFixed(1);
                document.getElementById('avgUdpSpeed').textContent = avgUdp.toFixed(1);
                document.getElementById('avgPacketLoss').textContent = avgLoss.toFixed(2);
                document.getElementById('avgLatency').textContent = avgLatency.toFixed(1);
                document.getElementById('avgJitter').textContent = avgJitter.toFixed(2);
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

                // 更新颜色状态
                this.updateStatColors();
            }

            // 更新统计数字的颜色状态
            updateStatColors() {
                const onlineCount = parseInt(document.getElementById('onlineCount').textContent);
                const avgTcp = parseFloat(document.getElementById('avgTcpSpeed').textContent);
                const avgLoss = parseFloat(document.getElementById('avgPacketLoss').textContent);

                // 在线服务器数量颜色
                const onlineEl = document.getElementById('onlineCount');
                onlineEl.className = `number ${onlineCount >= 10 ? 'good' : onlineCount >= 8 ? 'warning' : 'danger'}`;

                // TCP速度颜色
                const tcpEl = document.getElementById('avgTcpSpeed');
                tcpEl.className = `number ${avgTcp >= 500 ? 'good' : avgTcp >= 200 ? 'warning' : 'danger'}`;

                // 丢包率颜色
                const lossEl = document.getElementById('avgPacketLoss');
                lossEl.className = `number ${avgLoss <= 0.1 ? 'good' : avgLoss <= 1 ? 'warning' : 'danger'}`;
            }

            // 初始化图表
            initChart() {
                const ctx = document.getElementById('performanceChart').getContext('2d');
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.generateTimeLabels(),
                        datasets: [
                            {
                                label: 'TCP平均速度 (Mbps)',
                                data: this.generateTrendData(850, 50),
                                borderColor: '#3498db',
                                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                tension: 0.4
                            },
                            {
                                label: 'UDP平均速度 (Mbps)',
                                data: this.generateTrendData(95, 10),
                                borderColor: '#e74c3c',
                                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                                tension: 0.4
                            },
                            {
                                label: '平均延迟 (ms)',
                                data: this.generateTrendData(12, 5),
                                borderColor: '#f39c12',
                                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1'
                            },
                            {
                                label: '平均丢包率 (%)',
                                data: this.generateTrendData(0.1, 0.3),
                                borderColor: '#9b59b6',
                                backgroundColor: 'rgba(155, 89, 182, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '网络性能趋势 (最近24小时) - iperf3测试指标'
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: '速度 (Mbps)'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '延迟(ms) / 丢包率(%)'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
            }

            // 生成时间标签
            generateTimeLabels() {
                const labels = [];
                for (let i = 23; i >= 0; i--) {
                    const time = new Date(Date.now() - i * 3600000);
                    labels.push(time.getHours() + ':00');
                }
                return labels;
            }

            // 生成趋势数据
            generateTrendData(base, variance) {
                const data = [];
                for (let i = 0; i < 24; i++) {
                    const value = base + (Math.random() - 0.5) * variance;
                    data.push(Math.max(0, value).toFixed(2));
                }
                return data;
            }

            // 开始自动刷新
            startAutoRefresh() {
                setInterval(() => {
                    this.refreshData();
                }, 30000); // 30秒刷新一次
            }

            // 刷新数据
            refreshData() {
                this.servers = this.generateServerData();
                this.renderServers();
                this.updateStats();

                // 更新图表数据
                this.chart.data.datasets[0].data = this.generateTrendData(850, 50);
                this.chart.data.datasets[1].data = this.generateTrendData(95, 10);
                this.chart.data.datasets[2].data = this.generateTrendData(12, 5);
                this.chart.data.datasets[3].data = this.generateTrendData(0.1, 0.3);
                this.chart.update();
            }
        }

        // 全局函数
        function refreshData() {
            if (window.monitor) {
                window.monitor.refreshData();
            }
        }

        function exportData() {
            if (window.monitor) {
                const data = {
                    timestamp: new Date().toISOString(),
                    servers: window.monitor.servers,
                    summary: {
                        onlineCount: document.getElementById('onlineCount').textContent,
                        avgTcpSpeed: document.getElementById('avgTcpSpeed').textContent,
                        avgUdpSpeed: document.getElementById('avgUdpSpeed').textContent,
                        avgPacketLoss: document.getElementById('avgPacketLoss').textContent,
                        avgLatency: document.getElementById('avgLatency').textContent,
                        avgJitter: document.getElementById('avgJitter').textContent
                    }
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `server_monitor_${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.monitor = new ServerMonitor();
        });
    </script>
</body>
</html>
