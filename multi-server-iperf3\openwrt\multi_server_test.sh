#!/bin/bash

# 多服务器iperf3测试脚本 - OpenWRT端
# 功能：测试12台服务器的网络性能，存储到SQLite，支持并发测试

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 加载配置文件
source ./config.conf

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "DEBUG") [ "$LOG_LEVEL" = "DEBUG" ] && echo -e "${CYAN}[DEBUG]${NC} $timestamp - $message" ;;
        "INFO")  echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $timestamp - $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
    esac
    
    # 写入日志文件
    echo "[$level] $timestamp - $message" >> "$LOG_FILE"
}

# 初始化数据库
init_database() {
    log "INFO" "初始化SQLite数据库..."
    
    sqlite3 "$DB_FILE" <<EOF
CREATE TABLE IF NOT EXISTS servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    ip_address TEXT NOT NULL,
    port INTEGER NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS test_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    server_name TEXT NOT NULL,
    server_ip TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    test_type TEXT NOT NULL,
    duration INTEGER NOT NULL,
    
    -- TCP测试结果
    tcp_bandwidth_bps INTEGER,
    tcp_bandwidth_mbps REAL,
    tcp_retransmits INTEGER,
    tcp_cwnd INTEGER,
    tcp_rtt_ms REAL,
    
    -- UDP测试结果
    udp_bandwidth_bps INTEGER,
    udp_bandwidth_mbps REAL,
    udp_jitter_ms REAL,
    udp_lost_packets INTEGER,
    udp_total_packets INTEGER,
    udp_lost_percent REAL,
    
    -- 连接质量
    connect_time_ms REAL,
    test_start_time DATETIME,
    test_end_time DATETIME,
    
    -- 原始数据和状态
    raw_json TEXT,
    status TEXT DEFAULT 'completed',
    error_message TEXT,
    
    FOREIGN KEY (server_id) REFERENCES servers (id)
);

CREATE TABLE IF NOT EXISTS test_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_start DATETIME DEFAULT CURRENT_TIMESTAMP,
    session_end DATETIME,
    total_servers INTEGER,
    successful_tests INTEGER,
    failed_tests INTEGER,
    session_type TEXT DEFAULT 'scheduled'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_test_results_timestamp ON test_results(timestamp);
CREATE INDEX IF NOT EXISTS idx_test_results_server ON test_results(server_name);
CREATE INDEX IF NOT EXISTS idx_test_results_type ON test_results(test_type);
CREATE INDEX IF NOT EXISTS idx_servers_enabled ON servers(enabled);
EOF

    if [ $? -eq 0 ]; then
        log "SUCCESS" "数据库初始化完成"
        init_servers_config
    else
        log "ERROR" "数据库初始化失败"
        exit 1
    fi
}

# 初始化服务器配置
init_servers_config() {
    log "INFO" "初始化服务器配置..."
    
    for server_config in "${SERVERS[@]}"; do
        IFS='|' read -r name ip port desc enabled <<< "$server_config"
        
        # 插入或更新服务器配置
        sqlite3 "$DB_FILE" <<EOF
INSERT OR REPLACE INTO servers (name, ip_address, port, description, enabled)
VALUES ('$name', '$ip', $port, '$desc', $([ "$enabled" = "true" ] && echo 1 || echo 0));
EOF
    done
    
    log "SUCCESS" "服务器配置初始化完成"
}

# 检查服务器连通性
check_server_connectivity() {
    local server_ip=$1
    local server_port=$2
    local timeout=5
    
    # 使用nc检查端口连通性
    if command -v nc >/dev/null 2>&1; then
        nc -z -w$timeout "$server_ip" "$server_port" >/dev/null 2>&1
        return $?
    else
        # 使用telnet作为备选
        timeout $timeout telnet "$server_ip" "$server_port" >/dev/null 2>&1
        return $?
    fi
}

# 执行单个服务器的TCP测试
run_tcp_test() {
    local server_name=$1
    local server_ip=$2
    local server_port=$3
    local server_id=$4
    
    log "INFO" "开始TCP测试: $server_name ($server_ip:$server_port)"
    
    local json_file="/tmp/tcp_${server_name}_$(date +%s).json"
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    local connect_start=$(date +%s%3N)
    
    # 执行iperf3 TCP测试
    if timeout $TEST_TIMEOUT iperf3 -c "$server_ip" -p "$server_port" -t $TEST_DURATION -P $TCP_PARALLEL_STREAMS -J > "$json_file" 2>/dev/null; then
        local connect_end=$(date +%s%3N)
        local connect_time=$((connect_end - connect_start))
        local end_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        # 解析JSON结果
        local bandwidth_bps=$(cat "$json_file" | grep -o '"bits_per_second":[0-9.]*' | tail -1 | cut -d':' -f2)
        local bandwidth_mbps=$(echo "scale=2; $bandwidth_bps / 1000000" | bc 2>/dev/null || echo "0")
        local retransmits=$(cat "$json_file" | grep -o '"retransmits":[0-9]*' | cut -d':' -f2 | head -1)
        local cwnd=$(cat "$json_file" | grep -o '"cwnd":[0-9]*' | cut -d':' -f2 | head -1)
        local raw_json=$(cat "$json_file" | tr '\n' ' ' | sed "s/'/\'\'/g")
        
        # 存储到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    server_id, server_name, server_ip, test_type, duration,
    tcp_bandwidth_bps, tcp_bandwidth_mbps, tcp_retransmits, tcp_cwnd,
    connect_time_ms, test_start_time, test_end_time, raw_json, status
) VALUES (
    $server_id, '$server_name', '$server_ip', 'TCP', $TEST_DURATION,
    $bandwidth_bps, $bandwidth_mbps, ${retransmits:-0}, ${cwnd:-0},
    $connect_time, '$start_time', '$end_time', '$raw_json', 'completed'
);
EOF
        
        log "SUCCESS" "TCP测试完成: $server_name - ${bandwidth_mbps} Mbps, 重传: ${retransmits:-0}"
        
        # 检查告警条件
        if [ "$ALERT_ENABLED" = "true" ] && [ "$(echo "$bandwidth_mbps < $ALERT_TCP_MIN_SPEED" | bc)" -eq 1 ]; then
            log "WARN" "TCP速度告警: $server_name 速度 ${bandwidth_mbps} Mbps 低于阈值 ${ALERT_TCP_MIN_SPEED} Mbps"
        fi
        
        rm -f "$json_file"
        return 0
    else
        local end_time=$(date '+%Y-%m-%d %H:%M:%S')
        log "ERROR" "TCP测试失败: $server_name"
        
        # 记录错误到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    server_id, server_name, server_ip, test_type, duration,
    test_start_time, test_end_time, status, error_message
) VALUES (
    $server_id, '$server_name', '$server_ip', 'TCP', $TEST_DURATION,
    '$start_time', '$end_time', 'failed', 'iperf3 TCP test timeout or connection failed'
);
EOF
        rm -f "$json_file"
        return 1
    fi
}

# 执行单个服务器的UDP测试
run_udp_test() {
    local server_name=$1
    local server_ip=$2
    local server_port=$3
    local server_id=$4
    
    log "INFO" "开始UDP测试: $server_name ($server_ip:$server_port)"
    
    local json_file="/tmp/udp_${server_name}_$(date +%s).json"
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 执行iperf3 UDP测试
    if timeout $TEST_TIMEOUT iperf3 -c "$server_ip" -p "$server_port" -u -b "$UDP_BANDWIDTH" -t $TEST_DURATION -J > "$json_file" 2>/dev/null; then
        local end_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        # 解析JSON结果
        local bandwidth_bps=$(cat "$json_file" | grep -o '"bits_per_second":[0-9.]*' | tail -1 | cut -d':' -f2)
        local bandwidth_mbps=$(echo "scale=2; $bandwidth_bps / 1000000" | bc 2>/dev/null || echo "0")
        local jitter=$(cat "$json_file" | grep -o '"jitter_ms":[0-9.]*' | cut -d':' -f2)
        local lost_packets=$(cat "$json_file" | grep -o '"lost_packets":[0-9]*' | cut -d':' -f2)
        local total_packets=$(cat "$json_file" | grep -o '"packets":[0-9]*' | cut -d':' -f2)
        local lost_percent=$(cat "$json_file" | grep -o '"lost_percent":[0-9.]*' | cut -d':' -f2)
        local raw_json=$(cat "$json_file" | tr '\n' ' ' | sed "s/'/\'\'/g")
        
        # 存储到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    server_id, server_name, server_ip, test_type, duration,
    udp_bandwidth_bps, udp_bandwidth_mbps, udp_jitter_ms,
    udp_lost_packets, udp_total_packets, udp_lost_percent,
    test_start_time, test_end_time, raw_json, status
) VALUES (
    $server_id, '$server_name', '$server_ip', 'UDP', $TEST_DURATION,
    $bandwidth_bps, $bandwidth_mbps, ${jitter:-0},
    ${lost_packets:-0}, ${total_packets:-0}, ${lost_percent:-0},
    '$start_time', '$end_time', '$raw_json', 'completed'
);
EOF
        
        log "SUCCESS" "UDP测试完成: $server_name - ${bandwidth_mbps} Mbps, 丢包: ${lost_percent:-0}%, 抖动: ${jitter:-0} ms"
        
        # 检查告警条件
        if [ "$ALERT_ENABLED" = "true" ] && [ "$(echo "${lost_percent:-0} > $ALERT_UDP_MAX_LOSS" | bc)" -eq 1 ]; then
            log "WARN" "UDP丢包告警: $server_name 丢包率 ${lost_percent:-0}% 超过阈值 ${ALERT_UDP_MAX_LOSS}%"
        fi
        
        rm -f "$json_file"
        return 0
    else
        local end_time=$(date '+%Y-%m-%d %H:%M:%S')
        log "ERROR" "UDP测试失败: $server_name"
        
        # 记录错误到数据库
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    server_id, server_name, server_ip, test_type, duration,
    test_start_time, test_end_time, status, error_message
) VALUES (
    $server_id, '$server_name', '$server_ip', 'UDP', $TEST_DURATION,
    '$start_time', '$end_time', 'failed', 'iperf3 UDP test timeout or connection failed'
);
EOF
        rm -f "$json_file"
        return 1
    fi
}

# 测试单个服务器
test_single_server() {
    local server_config=$1
    IFS='|' read -r name ip port desc enabled <<< "$server_config"
    
    if [ "$enabled" != "true" ]; then
        log "INFO" "跳过禁用的服务器: $name"
        return 0
    fi
    
    # 获取服务器ID
    local server_id=$(sqlite3 "$DB_FILE" "SELECT id FROM servers WHERE name='$name';")
    
    log "INFO" "开始测试服务器: $name ($ip:$port)"
    
    # 检查连通性
    if ! check_server_connectivity "$ip" "$port"; then
        log "ERROR" "服务器连通性检查失败: $name ($ip:$port)"
        
        # 记录连通性失败
        sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_results (
    server_id, server_name, server_ip, test_type, duration,
    status, error_message
) VALUES (
    $server_id, '$name', '$ip', 'CONNECTIVITY', 0,
    'failed', 'Server connectivity check failed'
);
EOF
        return 1
    fi
    
    local tcp_success=false
    local udp_success=false
    
    # 执行TCP测试
    if run_tcp_test "$name" "$ip" "$port" "$server_id"; then
        tcp_success=true
    fi
    
    # 测试间隔
    sleep $TEST_INTERVAL
    
    # 执行UDP测试
    if run_udp_test "$name" "$ip" "$port" "$server_id"; then
        udp_success=true
    fi
    
    if [ "$tcp_success" = true ] && [ "$udp_success" = true ]; then
        log "SUCCESS" "服务器测试完成: $name"
        return 0
    else
        log "WARN" "服务器测试部分失败: $name"
        return 1
    fi
}

# 并发测试所有服务器
test_all_servers() {
    log "INFO" "开始测试所有服务器..."
    
    # 创建测试会话记录
    local session_start=$(date '+%Y-%m-%d %H:%M:%S')
    local enabled_servers=($(printf '%s\n' "${SERVERS[@]}" | grep '|true$'))
    local total_servers=${#enabled_servers[@]}
    
    sqlite3 "$DB_FILE" <<EOF
INSERT INTO test_sessions (session_start, total_servers, session_type)
VALUES ('$session_start', $total_servers, 'scheduled');
EOF
    local session_id=$(sqlite3 "$DB_FILE" "SELECT last_insert_rowid();")
    
    local successful_tests=0
    local failed_tests=0
    local active_jobs=0
    local job_pids=()
    
    # 并发测试
    for server_config in "${enabled_servers[@]}"; do
        # 控制并发数量
        while [ $active_jobs -ge $MAX_CONCURRENT_TESTS ]; do
            # 等待任意一个任务完成
            for i in "${!job_pids[@]}"; do
                if ! kill -0 "${job_pids[$i]}" 2>/dev/null; then
                    wait "${job_pids[$i]}"
                    local exit_code=$?
                    if [ $exit_code -eq 0 ]; then
                        ((successful_tests++))
                    else
                        ((failed_tests++))
                    fi
                    unset job_pids[$i]
                    ((active_jobs--))
                    break
                fi
            done
            sleep 1
        done
        
        # 启动新的测试任务
        test_single_server "$server_config" &
        local pid=$!
        job_pids+=($pid)
        ((active_jobs++))
        
        log "DEBUG" "启动测试任务 PID: $pid, 活跃任务数: $active_jobs"
    done
    
    # 等待所有任务完成
    for pid in "${job_pids[@]}"; do
        if [ -n "$pid" ]; then
            wait $pid
            local exit_code=$?
            if [ $exit_code -eq 0 ]; then
                ((successful_tests++))
            else
                ((failed_tests++))
            fi
        fi
    done
    
    # 更新测试会话记录
    local session_end=$(date '+%Y-%m-%d %H:%M:%S')
    sqlite3 "$DB_FILE" <<EOF
UPDATE test_sessions 
SET session_end = '$session_end', 
    successful_tests = $successful_tests, 
    failed_tests = $failed_tests
WHERE id = $session_id;
EOF
    
    log "SUCCESS" "所有服务器测试完成 - 成功: $successful_tests, 失败: $failed_tests"
    
    # 数据清理
    if [ "$AUTO_CLEANUP" = "true" ]; then
        cleanup_old_data
    fi
}

# 清理旧数据
cleanup_old_data() {
    log "INFO" "清理 $DATA_RETENTION_DAYS 天前的数据..."
    
    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM test_results WHERE timestamp < datetime('now', '-$DATA_RETENTION_DAYS days');")
    
    if [ "$count" -gt 0 ]; then
        sqlite3 "$DB_FILE" <<EOF
DELETE FROM test_results WHERE timestamp < datetime('now', '-$DATA_RETENTION_DAYS days');
DELETE FROM test_sessions WHERE session_start < datetime('now', '-$DATA_RETENTION_DAYS days');
VACUUM;
EOF
        log "SUCCESS" "已清理 $count 条旧记录"
    fi
}

# 显示测试摘要
show_summary() {
    log "INFO" "生成测试摘要..."
    
    echo ""
    echo "================================"
    echo "📊 最新测试结果摘要"
    echo "================================"
    
    sqlite3 -header -column "$DB_FILE" <<EOF
SELECT 
    server_name as '服务器',
    test_type as '类型',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%.2f Mbps', tcp_bandwidth_mbps)
        WHEN test_type = 'UDP' THEN printf('%.2f Mbps', udp_bandwidth_mbps)
        ELSE 'N/A'
    END as '带宽',
    CASE 
        WHEN test_type = 'TCP' THEN printf('%d 次', tcp_retransmits)
        WHEN test_type = 'UDP' THEN printf('%.2f%%', udp_lost_percent)
        ELSE 'N/A'
    END as '重传/丢包',
    status as '状态'
FROM test_results 
WHERE timestamp > datetime('now', '-1 hour')
ORDER BY server_name, test_type;
EOF
}

# 主函数
main() {
    echo "🚀 多服务器iperf3测试系统启动"
    echo "测试目标: ${#SERVERS[@]} 台服务器"
    echo "================================"
    
    # 初始化
    init_database
    
    # 执行测试
    test_all_servers
    
    # 显示摘要
    show_summary
    
    log "SUCCESS" "测试周期完成！"
}

# 启动Web服务器
start_web_server() {
    log "INFO" "启动Web可视化服务器..."

    # 检查Web目录
    if [ ! -d "$WEB_DIR" ]; then
        log "WARN" "Web目录不存在，创建中..."
        mkdir -p "$WEB_DIR"
    fi

    # 启动简单的HTTP服务器
    if command -v python3 >/dev/null 2>&1; then
        cd "$WEB_DIR"
        python3 -m http.server $WEB_PORT >/dev/null 2>&1 &
        local web_pid=$!
        echo $web_pid > ../web_server.pid
        log "SUCCESS" "Web服务器已启动 - 端口: $WEB_PORT, PID: $web_pid"
        log "INFO" "访问地址: http://$(hostname -I | awk '{print $1}'):$WEB_PORT"
        cd "$SCRIPT_DIR"
    else
        log "WARN" "Python3未安装，无法启动Web服务器"
    fi
}

# 停止Web服务器
stop_web_server() {
    if [ -f "web_server.pid" ]; then
        local web_pid=$(cat web_server.pid)
        if kill -0 $web_pid 2>/dev/null; then
            kill $web_pid
            log "SUCCESS" "Web服务器已停止"
        fi
        rm -f web_server.pid
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    case "${1:-test}" in
        "test")
            main
            ;;
        "web")
            start_web_server
            ;;
        "stop-web")
            stop_web_server
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo "命令:"
            echo "  test      执行测试 (默认)"
            echo "  web       启动Web服务器"
            echo "  stop-web  停止Web服务器"
            echo "  help      显示帮助"
            ;;
        *)
            echo "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
fi
